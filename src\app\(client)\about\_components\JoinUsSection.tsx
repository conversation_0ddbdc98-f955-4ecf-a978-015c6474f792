import { Smartphone, Globe, Download } from "lucide-react";
import Link from "next/link";
import { But<PERSON> } from "@/components/ui/button";

const platforms = [
  {
    name: "Website",
    url: "www.eefexpress.shop",
    icon: Globe,
    bgColor: "bg-blue-100",
    iconColor: "text-blue-600"
  },
  {
    name: "Play Store",
    url: "Play Store",
    icon: Smartphone,
    bgColor: "bg-green-100", 
    iconColor: "text-green-600"
  },
  {
    name: "App Store",
    url: "App Store",
    icon: Download,
    bgColor: "bg-gray-100",
    iconColor: "text-gray-600"
  }
];

export default function JoinUsSection() {
  return (
    <section className="mb-16">
      <div className="flex items-center gap-4 mb-8">
        <div className="flex items-center justify-center w-12 h-12 bg-indigo-100 rounded-xl">
          <Smartphone className="w-6 h-6 text-indigo-600" />
        </div>
        <h2 className="section-title text-gray-900">📲 Join Us</h2>
      </div>

      <div className="bg-gradient-to-r from-indigo-600 via-purple-600 to-pink-600 text-white rounded-2xl p-8">
        <div className="text-center mb-8">
          <h3 className="text-2xl font-bold mb-4">Experience a Smarter Way</h3>
          <p className="text-indigo-100 leading-relaxed max-w-2xl mx-auto">
            Visit www.eefexpress.shop or download the EEF EXPRESS app on Play Store or App Store and experience a smarter way to shop, eat, and send packages.
          </p>
        </div>

        <div className="grid md:grid-cols-3 gap-4 mb-8">
          {platforms.map((platform, index) => {
            const IconComponent = platform.icon;
            return (
              <div key={index} className="bg-white/10 backdrop-blur-sm rounded-xl p-4 text-center border border-white/20">
                <div className={`w-10 h-10 ${platform.bgColor} rounded-full flex items-center justify-center mx-auto mb-3`}>
                  <IconComponent className={`w-5 h-5 ${platform.iconColor}`} />
                </div>
                <p className="font-medium">{platform.name}</p>
                <p className="text-sm text-indigo-200">{platform.url}</p>
              </div>
            );
          })}
        </div>

        <div className="text-center">
          <div className="bg-white/20 backdrop-blur-sm rounded-xl p-6 border border-white/30">
            <p className="text-xl font-semibold mb-2">Let EEF EXPRESS do the running — while you relax.</p>
          </div>
        </div>
      </div>
    </section>
  );
}
