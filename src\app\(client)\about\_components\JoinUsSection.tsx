import Link from "next/link";

import { But<PERSON> } from "@/components/ui/button";

export default function JoinUsSection() {
  return (
    <section className="mb-16">
      <div className="bg-gradient-to-r from-[#0c1922] via-[#013356] to-[#0c1922] text-white rounded-2xl p-8 py-18 text-center">
        <h3 className="text-2xl font-bold mb-4">Experience a Smarter Way</h3>
        <p className="text-gray-200 leading-relaxed max-w-2xl mx-auto mb-8">
          Visit www.eefexpress.shop or download the EEF EXPRESS app and experience a smarter way to shop, eat, and send
          packages.
        </p>

        <div className="flex flex-col sm:flex-row gap-4 justify-center items-center mb-6">
          <Button
            asChild
            className="bg-black hover:bg-gray-800 text-white px-6 py-3 rounded-lg flex items-center gap-2"
          >
            <Link href="#" target="_blank">
              <svg className="w-6 h-6" viewBox="0 0 24 24" fill="currentColor">
                <path d="M17.05 20.28c-.98.95-2.05.8-3.08.35-1.09-.46-2.09-.48-3.24 0-1.44.62-2.2.44-3.06-.35C2.79 15.25 3.51 7.59 9.05 7.31c1.35.07 2.29.74 3.08.8 1.18-.24 2.31-.93 3.57-.84 1.51.12 2.65.72 3.4 1.8-3.12 1.87-2.38 5.98.48 7.13-.57 1.5-1.31 2.99-2.54 4.09l.01-.01zM12.03 7.25c-.15-2.23 1.66-4.07 3.74-4.25.29 2.58-2.34 4.5-3.74 4.25z" />
              </svg>
              App Store
            </Link>
          </Button>

          <Button
            asChild
            className="bg-green-600 hover:bg-green-700 text-white px-6 py-3 rounded-lg flex items-center gap-2"
          >
            <Link href="#" target="_blank">
              <svg className="w-6 h-6" viewBox="0 0 24 24" fill="currentColor">
                <path d="M3,20.5V3.5C3,2.91 3.34,2.39 3.84,2.15L13.69,12L3.84,21.85C3.34,21.61 3,21.09 3,20.5M16.81,15.12L6.05,21.34L14.54,12.85L16.81,15.12M20.16,10.81C20.5,11.08 20.75,11.5 20.75,12C20.75,12.5 20.53,12.9 20.18,13.18L17.89,14.5L15.39,12L17.89,9.5L20.16,10.81M6.05,2.66L16.81,8.88L14.54,11.15L6.05,2.66Z" />
              </svg>
              Play Store
            </Link>
          </Button>
        </div>

        <p className="text-gray-300 font-medium text-sm">Let EEF EXPRESS do the running — while you relax.</p>
      </div>
    </section>
  );
}
