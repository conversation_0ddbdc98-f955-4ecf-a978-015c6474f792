import { Product } from "@/types/product";
import { apiRequest } from "@/utils/axios-utils";

export interface ProductsFilterParams {
  page?: number;
  pageSize?: number;
  sort?: string;
  search?: string;
  minPrice?: number;
  maxPrice?: number;
  category?: number;
  inStock?: boolean;
  minRating?: number;
}

export interface Pagination {
  page: number;
  pageSize: number;
  pageCount: number;
  total: number;
}

export interface ProductsResponse {
  data: {
    results: Product[];
    pagination: Pagination;
  };
  meta: null;
}

export const getProducts = async (params: ProductsFilterParams = {}) => {
  // Convert params object to URL query parameters
  const queryParams = new URLSearchParams();

  // Add all non-undefined params to query string
  Object.entries(params).forEach(([key, value]) => {
    if (value !== undefined) {
      queryParams.append(key, String(value));
    }
  });

  const response = await apiRequest<ProductsResponse>({
    url: `/products?${queryParams.toString()}`,
    method: "GET",
    showToast: false,
  });

  return response.data;
};
