import { CartData } from "@/types/cart";
import { apiRequest } from "@/utils/axios-utils";

export interface UpdateCartResponse {
  data: CartData;
  meta: null;
}

export const updateItemQuantityInCart = async (itemId: number, quantity: number) => {
  const response = await apiRequest<UpdateCartResponse>({
    url: `/cart/items/${itemId}`,
    method: "PUT",
    data: {
      quantity: quantity,
    },
    showToast: false,
  });
  return response;
};
