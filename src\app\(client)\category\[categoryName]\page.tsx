"use client";

import React, { useEffect, useState } from "react";
import Image from "next/image";
import Link from "next/link";
import { useParams } from "next/navigation";

import { useGetCategoriesWithCountQuery } from "@/app/(client)/_hooks/useGetCategoriesWithCountQuery";
import useGetProductsQuery from "@/app/(client)/_hooks/useGetProductsQuery";
import PaginationWithEllipsis from "@/components/PaginationWithEllipsis";
import ProductCard from "@/components/ProductCard";
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from "@/components/ui/breadcrumb";
import { Skeleton } from "@/components/ui/skeleton";

function CategoryPage() {
  const params = useParams();
  const categoryId = Number(params?.categoryName);

  const [currentPage, setCurrentPage] = useState(1);
  const pageSize = 12;

  // Fetch category details to get the name
  const { data: categories } = useGetCategoriesWithCountQuery();
  const category = categories?.find((cat) => cat.id === categoryId);
  const categoryName = category?.name || "Category";

  // Fetch products for this category ID with pagination
  const {
    data: productsData,
    isLoading,
    isError,
  } = useGetProductsQuery({
    page: currentPage,
    pageSize: pageSize,
    category: categoryId,
  });

  const products = productsData?.results || [];
  const totalPages = productsData?.pagination?.pageCount || 0;

  // Handle page change
  const handlePageChange = (page: number) => {
    setCurrentPage(page);
    // Scroll to top when changing page
    window.scrollTo({ top: 0, behavior: "smooth" });
  };

  // Reset to page 1 when category changes
  useEffect(() => {
    setCurrentPage(1);
  }, [categoryId]);

  return (
    <div className="max-width-wrapper">
      {/* header */}
      <div className="relative w-full text-white bg-gray-100 overflow-hidden p-6 py-12 md:p-16 rounded-lg tracking-tight">
        {/* background image */}
        <div className="absolute inset-0 overflow-hidden">
          <Image src={"/assets/images/exclusive-sales.webp"} alt={categoryName} fill className="object-cover" />
          <div className="absolute inset-0 bg-black opacity-60"></div>
        </div>

        {/* content wrapper with higher z-index */}
        <div className="relative z-10">
          <Breadcrumb>
            <BreadcrumbList className="text-gray-300">
              <BreadcrumbItem>
                <BreadcrumbLink className="hover:text-white" asChild>
                  <Link href="/">Home</Link>
                </BreadcrumbLink>
              </BreadcrumbItem>
              <BreadcrumbSeparator />
              <BreadcrumbItem>
                <BreadcrumbLink className="hover:text-white" asChild>
                  <Link href="/shop">Categories</Link>
                </BreadcrumbLink>
              </BreadcrumbItem>
              <BreadcrumbSeparator />
              <BreadcrumbItem>
                <BreadcrumbPage className="text-white font-medium">{categoryName}</BreadcrumbPage>
              </BreadcrumbItem>
            </BreadcrumbList>
          </Breadcrumb>

          {/* Category title + description */}
          <div className="mt-5 space-y-2">
            <h1 className="section-title capitalize">{categoryName}</h1>
            <p className="text-gray-300">Browse our collection of {categoryName.toLowerCase()}.</p>
          </div>
        </div>
      </div>

      {/* Loading state */}
      {isLoading && (
        <div className="mt-10 mb-20 grid grid-cols-2 gap-4 gap-y-8 md:grid-cols-3 xl:grid-cols-4">
          {Array(8)
            .fill(0)
            .map((_, index) => (
              <div key={index} className="space-y-3">
                <Skeleton className="h-[13rem] w-full rounded-xl" />
                <Skeleton className="h-4 w-2/3" />
                <Skeleton className="h-4 w-1/3" />
              </div>
            ))}
        </div>
      )}

      {/* Error state */}
      {isError && (
        <div className="mt-10 mb-20 flex justify-center items-center h-40">
          <p className="text-red-500">Failed to load products. Please try again later.</p>
        </div>
      )}

      {/* Empty state */}
      {!isLoading && !isError && products.length === 0 && (
        <div className="mt-10 mb-20 flex justify-center items-center h-40">
          <p className="text-gray-500">No products found in this category.</p>
        </div>
      )}

      {/* Products section */}
      {!isLoading && !isError && products.length > 0 && (
        <>
          <div className="mt-10 mb-8 grid grid-cols-2 gap-4 gap-y-8 md:grid-cols-3 xl:grid-cols-4">
            {products?.map((product) => (
              <ProductCard
                key={product.id}
                name={product.name}
                image={product.images[0]?.url}
                price={product.price}
                ratings={product.ratings}
                stock={product.stock}
                _id={product.id}
              />
            ))}
          </div>

          {/* Pagination */}
          {totalPages > 1 && (
            <div className="mb-20 mt-3">
              <PaginationWithEllipsis
                currentPage={currentPage}
                totalPages={totalPages}
                onPageChange={handlePageChange}
              />
            </div>
          )}
        </>
      )}
    </div>
  );
}

export default CategoryPage;
