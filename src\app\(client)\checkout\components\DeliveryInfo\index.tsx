import React from "react";
import { Control } from "react-hook-form";

import { FormControl, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { CheckoutFormValues } from "@/lib/schemas/checkoutFormSchema";

interface DeliveryInfoProps {
  control: Control<CheckoutFormValues>;
}

function DeliveryInfo({ control }: DeliveryInfoProps) {
  return (
    <div className="w-full border rounded-lg p-5 tracking-tight space-y-5">
      {/* header */}
      <div className="mb-8 flex justify-between items-end">
        <div className="space-y-1">
          <h3 className="font-bold text-xl">Delivery Info</h3>
          <p className="text-sm text-gray-400 tracking-normal">Please enter your delivery details</p>
        </div>

        <p className="text-gray-400 text-sm">Step 1 of 4</p>
      </div>
      {/* header ends */}

      {/* body */}
      <div className="space-y-4">
        {/* input group */}
        <FormField
          control={control}
          name="shippingAddress.name"
          render={({ field }) => (
            <FormItem className="space-y-2.5">
              <FormLabel>Full Name</FormLabel>
              <FormControl>
                <Input {...field} placeholder="John Doe" />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
          {/* input group */}
          <FormField
            control={control}
            name="shippingAddress.addressLine1"
            render={({ field }) => (
              <FormItem className="space-y-2.5">
                <FormLabel>Address Line 1</FormLabel>
                <FormControl>
                  <Input {...field} placeholder="123 Main Street" />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
          {/* input group */}
          <FormField
            control={control}
            name="shippingAddress.addressLine2"
            render={({ field }) => (
              <FormItem className="space-y-2.5">
                <FormLabel>Address Line 2</FormLabel>
                <FormControl>
                  <Input {...field} placeholder="Apartment 4B" />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>

        <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
          {/* input group */}
          <FormField
            control={control}
            name="shippingAddress.apartmentOrVilla"
            render={({ field }) => (
              <FormItem className="space-y-2.5">
                <FormLabel>Apartment or Villa</FormLabel>
                <Select onValueChange={field.onChange} defaultValue={field.value}>
                  <FormControl>
                    <SelectTrigger className="w-full">
                      <SelectValue placeholder="Select type" />
                    </SelectTrigger>
                  </FormControl>
                  <SelectContent>
                    <SelectItem value="Apartment">Apartment</SelectItem>
                    <SelectItem value="Villa">Villa</SelectItem>
                  </SelectContent>
                </Select>
                <FormMessage />
              </FormItem>
            )}
          />
          {/* input group */}
          <FormField
            control={control}
            name="shippingAddress.emirate"
            render={({ field }) => (
              <FormItem className="space-y-2.5">
                <FormLabel>Emirate</FormLabel>
                <Select onValueChange={field.onChange} defaultValue={field.value}>
                  <FormControl>
                    <SelectTrigger className="w-full">
                      <SelectValue placeholder="Select emirate" />
                    </SelectTrigger>
                  </FormControl>
                  <SelectContent>
                    <SelectItem value="Abu Dhabi">Abu Dhabi</SelectItem>
                    <SelectItem value="Dubai">Dubai</SelectItem>
                    <SelectItem value="Sharjah">Sharjah</SelectItem>
                    <SelectItem value="Ajman">Ajman</SelectItem>
                    <SelectItem value="Umm Al Quwain">Umm Al Quwain</SelectItem>
                    <SelectItem value="Ras Al Khaimah">Ras Al Khaimah</SelectItem>
                    <SelectItem value="Fujairah">Fujairah</SelectItem>
                  </SelectContent>
                </Select>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>

        <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
          {/* Phone Number Field */}
          <FormField
            control={control}
            name="shippingAddress.phoneNumber"
            render={({ field }) => (
              <FormItem className="space-y-2.5">
                <FormLabel>Phone Number</FormLabel>
                <FormControl>
                  <Input {...field} type="tel" placeholder="+971 50 123 4567" />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
          {/* Email Address Field */}
          <FormField
            control={control}
            name="customerEmail"
            render={({ field }) => (
              <FormItem className="space-y-2.5">
                <FormLabel>Email Address</FormLabel>
                <FormControl>
                  <Input {...field} type="email" placeholder="<EMAIL>" />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>
      </div>
    </div>
  );
}

export default DeliveryInfo;
