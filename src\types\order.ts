import { ProductImage } from "./product";

export interface OrderProduct {
  id: number;
  quantity: number;
  product: {
    id: number;
    documentId: string;
    name: string;
    description: string;
    images: ProductImage[];
    price: number;
    stock: number;
    ratings: number;
    createdAt: string;
    updatedAt: string;
    publishedAt: string;
    locale: string | null;
  };
}

export interface ShippingAddress {
  id: number;
  addressLine1: string;
  addressLine2: string;
  apartmentOrVilla: string;
  emirate: string;
  name: string;
  phoneNumber: string;
}

export interface OrderStatus {
  id: number;
  shippingStatus: string;
  timestamp: string;
  locationNote?: string;
}

export interface Order {
  id: number;
  documentId: string;
  deliveryType: string;
  deliveryFee: number;
  totalAmount: number;
  subTotal: number;
  scheduledDateTime: string | null;
  paymentMethod: string;
  paymentStatus: string;
  createdAt: string;
  updatedAt: string;
  publishedAt: string;
  locale: string | null;
  stripeId: string | null;
  products: OrderProduct[];
  shippingAddress: ShippingAddress;
  orderStatus: OrderStatus[];
}

export interface Payment {
  success: boolean;
  status: string;
  transactionId: string;
  amount: number;
  paymentMethod: string;
  message: string;
  stripeSessionId?: string;
  checkoutUrl?: string;
}

export interface OrderData {
  success: boolean;
  order: Order;
  payment: Payment;
  requiresPayment: boolean;
  checkoutUrl?: string;
}

export interface Pagination {
  page: number;
  pageSize: number;
  pageCount: number;
  total: number;
}

export interface GetMyOrdersResponse {
  data: {
    pagination: Pagination;
    results: Order[];
  };
  meta: null;
}

export interface GetOrderDetailResponse {
  data: Order;
  meta: null;
}

export interface CreateOrderResponse {
  data: OrderData;
}
