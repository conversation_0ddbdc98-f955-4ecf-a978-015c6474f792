"use client";

import React, { useState } from "react";
import { Check, Eye, ShoppingCart } from "lucide-react";
import { toast } from "sonner";

import { useAddToCartMutation } from "@/app/(client)/_hooks/useAddToCartMutation";
import LoadingSpinner from "../LoadingSpinner";
import { Button } from "../ui/button";
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "../ui/tooltip";

interface ProductCardActionButtonsProps {
  isHovered: boolean;
  productId: number;
  stock: number;
}

const ProductCardActionButtons = ({ isHovered, productId, stock }: ProductCardActionButtonsProps) => {
  const addToCartMutation = useAddToCartMutation();
  const [isAddedToCart, setIsAddedToCart] = useState(false);

  const handleAddToCart = (e: React.MouseEvent) => {
    e.stopPropagation();
    e.preventDefault();

    if (stock === 0) {
      toast.info("Product out of stock.");
      return;
    }

    addToCartMutation.mutate(
      {
        productId: productId,
        quantity: 1,
      },
      {
        onSuccess: () => {
          setIsAddedToCart(true);
        },
      }
    );
  };

  const customTransition = "transition-all duration-300 ease-in-out";

  return (
    <TooltipProvider delayDuration={200}>
      <div className="absolute top-0 right-0 w-full p-4 flex flex-col items-end gap-2 pointer-events-none">
        {/* Cart Button */}
        <div
          className={`${customTransition} transform ${
            isHovered ? "translate-y-0 opacity-100 pointer-events-auto" : "-translate-y-4 opacity-0"
          }`}
        >
          <Tooltip>
            <TooltipTrigger asChild>
              <Button
                onClick={handleAddToCart}
                size="icon"
                variant={isAddedToCart ? "default" : "outline"}
                className={`${customTransition} ${
                  isAddedToCart ? "bg-green-600 text-white hover:bg-green-700" : "hover:bg-black hover:text-white"
                }`}
                aria-label={isAddedToCart ? "Added to Cart" : "Add to Cart"}
                disabled={addToCartMutation.isPending || isAddedToCart}
              >
                {addToCartMutation.isPending ? (
                  <LoadingSpinner size="sm" />
                ) : isAddedToCart ? (
                  <Check className="h-4 w-4" />
                ) : (
                  <ShoppingCart />
                )}
              </Button>
            </TooltipTrigger>
            <TooltipContent side="left" className="text-xs">
              {isAddedToCart ? "Added to cart" : "Add to cart"}
            </TooltipContent>
          </Tooltip>
        </div>

        {/* Eye Button */}
        <div
          className={`${customTransition} transform delay-150 ${
            isHovered ? "translate-y-0 opacity-100 pointer-events-auto" : "-translate-y-4 opacity-0"
          }`}
        >
          <Tooltip>
            <TooltipTrigger asChild>
              <Button
                size="icon"
                variant="outline"
                className={`${customTransition} hover:bg-black hover:text-white`}
                aria-label="View Product"
              >
                <Eye />
              </Button>
            </TooltipTrigger>
            <TooltipContent side="left" className="text-xs">
              View product
            </TooltipContent>
          </Tooltip>
        </div>
      </div>
    </TooltipProvider>
  );
};

export default ProductCardActionButtons;
