"use client";

import React from "react";
// import Image from "next/image";
import Link from "next/link";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import { z } from "zod";

import { But<PERSON> } from "@/components/ui/button";
import { Form, FormControl, FormField, FormItem, FormMessage } from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { useLoginMutation } from "../../_hooks/useLoginMutation";

// Zod schema for login validation
const loginSchema = z.object({
  email: z.string().email("Please enter a valid email address").min(1, "Email is required"),
  password: z.string().min(1, "Password is required").min(6, "Password must be at least 6 characters"),
});

type LoginFormValues = z.infer<typeof loginSchema>;

function LoginForm() {
  const loginMutation = useLoginMutation();

  const form = useForm<LoginFormValues>({
    resolver: zod<PERSON><PERSON><PERSON><PERSON>(loginSchema),
    defaultValues: {
      email: "",
      password: "",
    },
  });

  const onSubmit = (values: LoginFormValues) => {
    // Transform the form data to match the expected LoginCredentials interface
    const loginData = {
      identifier: values.email, // Using email as identifier
      password: values.password,
    };

    loginMutation.mutate(loginData);
  };

  return (
    <div className="text-center p-4 sm:p-8 shadow-lg rounded-lg w-full max-w-sm sm:max-w-md">
      {/* header */}
      <div className="space-y-2">
        <h1 className="text-2xl font-bold tracking-tight">Login to your account</h1>
        <p className="text-sm sm:text-base text-gray-500">Enter your login details to login to your account</p>
      </div>
      {/* header ends */}

      {/* body */}
      <div className="flex flex-col gap-2.5 mt-4">
        {/* <Button variant="outline" size={"lg"} className="w-full">
          <Image src={"/assets/svgs/google.svg"} height={20} width={20} alt="google" />
          Login with Google
        </Button>

        <div className="my-4 relative text-center text-sm after:absolute after:inset-0 after:top-1/2 after:z-0 after:flex after:items-center after:border-t after:border-border">
          <span className="relative z-10 bg-white px-3 text-gray-500 text-sm sm:text-base">OR</span>
        </div> */}

        {/* form fields */}
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-2.5">
            <FormField
              control={form.control}
              name="email"
              render={({ field }) => (
                <FormItem>
                  <FormControl>
                    <Input className="text-sm sm:text-base" type="email" placeholder="Email" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="password"
              render={({ field }) => (
                <FormItem>
                  <FormControl>
                    <Input className="text-sm sm:text-base" type="password" placeholder="Password" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <Button
              size={"lg"}
              className="text-sm sm:text-base w-full"
              type="submit"
              disabled={loginMutation.isPending}
            >
              {loginMutation.isPending ? "Logging in..." : "Login"}
            </Button>
          </form>
        </Form>
        {/* form fields ends */}

        <div className="text-center text-sm">
          Don't have an account yet?
          <Link href={"signup"} className="underline ml-1 font-medium hover:text-blue-500">
            Signup
          </Link>
        </div>
      </div>
      {/* body ends */}
    </div>
  );
}

export default LoginForm;
