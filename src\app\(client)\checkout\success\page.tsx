"use client";

import { Suspense, useEffect } from "react";
import Link from "next/link";
import { useSearchParams } from "next/navigation";
import { CheckCircle, Package, ShoppingBag, Truck } from "lucide-react";
import { toast } from "sonner";

import { But<PERSON> } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import useGetOrderDetailQuery from "../../orders/_hooks/useGetOrderDetailQuery";
import LoadingState from "./_components/LoadingState";
import StatusCard from "./_components/StatusCard";

function CheckoutSuccessContent() {
  const searchParams = useSearchParams();
  const orderId = searchParams?.get("order_id");
  const { data: order, isLoading } = useGetOrderDetailQuery(orderId!);

  useEffect(() => {
    if (order && !isLoading) {
      toast.success("Payment successful! Your order is confirmed.");
    }
  }, [order, isLoading]);

  if (isLoading) {
    return <LoadingState />;
  }

  return (
    <div className="max-width-wrapper py-12 px-4">
      <Card className="max-w-3xl mx-auto p-8 shadow-md">
        <div className="flex flex-col items-center text-center mb-8">
          <div className="bg-green-100 p-4 rounded-full mb-4">
            <CheckCircle className="h-16 w-16 text-green-600" />
          </div>
          <h1 className="text-3xl font-bold mb-2">Thank you for your order!</h1>
          <p className="text-gray-600 mb-2">
            Your order <span className="font-bold">#{order?.documentId}</span> has been successfully placed and
            confirmed.
          </p>
          <p className="text-gray-500 text-sm">We've sent a confirmation email to your inbox.</p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
          <StatusCard
            icon={<ShoppingBag className="h-6 w-6" />}
            title="Order Confirmed"
            description="Your order has been received and is being processed."
            active={true}
          />
          <StatusCard
            icon={<Package className="h-6 w-6" />}
            title="Preparing"
            description="We're preparing your items for delivery."
            active={false}
          />
          <StatusCard
            icon={<Truck className="h-6 w-6" />}
            title="On the Way"
            description="Your package is on its way to you."
            active={false}
          />
        </div>

        <div className="flex flex-col sm:flex-row gap-4 justify-center">
          <Button asChild size="lg">
            <Link href={`/orders`}>View Order Details</Link>
          </Button>
          <Button asChild variant="outline" size="lg">
            <Link href="/">Continue Shopping</Link>
          </Button>
        </div>
      </Card>
    </div>
  );
}

export default function CheckoutSuccess() {
  return (
    <Suspense fallback={<LoadingState />}>
      <CheckoutSuccessContent />
    </Suspense>
  );
}
