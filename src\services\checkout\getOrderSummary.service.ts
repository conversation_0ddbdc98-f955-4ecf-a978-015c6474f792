import { OrderSummary, OrderSummaryResponse } from "@/types/orderSummary";
import { apiRequest } from "@/utils/axios-utils";

export interface GetOrderSummaryParams {
  deliveryType?: string;
  scheduledDateTime?: string;
}

export const getOrderSummary = async (params: GetOrderSummaryParams = {}): Promise<OrderSummary> => {
  const queryParams = new URLSearchParams();

  if (params.deliveryType) {
    queryParams.append("deliveryType", params.deliveryType);
  }

  if (params.scheduledDateTime) {
    queryParams.append("scheduledDateTime", params.scheduledDateTime);
  }

  const queryString = queryParams.toString();
  const url = `/checkout/summary${queryString ? `?${queryString}` : ""}`;

  const response = await apiRequest<OrderSummaryResponse>({
    url,
    method: "GET",
    showToast: false,
  });

  return response.data;
};
