"use client";

import React from "react";
import Image from "next/image";
import { Calendar, MapPin, Package, User } from "lucide-react";

import { cn } from "@/lib/utils";
import { PickDrop } from "@/types/pickDrop";
import { formatDate } from "@/utils/formatDate";

interface PickDropOrderProps {
  order: PickDrop;
}

const statusColors = {
  Pending: "bg-yellow-100 text-yellow-700 ring-yellow-600/20",
  Confirmed: "bg-blue-100 text-blue-700 ring-blue-600/20",
  "In Transit": "bg-orange-100 text-orange-700 ring-orange-600/20",
  Completed: "bg-green-100 text-green-700 ring-green-600/20",
  Cancelled: "bg-red-100 text-red-700 ring-red-600/20",
};

const PickDropOrder = ({ order }: PickDropOrderProps) => {
  // Format dates for display
  const pickupDate = formatDate(new Date(order.pickupDateTime));
  const dropOffDate = formatDate(new Date(order.dropOffDateTime));

  return (
    <div className="bg-white rounded-lg border p-6 space-y-5">
      {/* Order Header */}
      <div className="flex flex-col sm:flex-row sm:justify-between gap-3 sm:items-center pb-4 border-b">
        <div className="space-y-1">
          <p className="text-sm text-gray-500">Order number:</p>
          <p className="font-semibold text-sm text-green-800">{order.documentId}</p>
        </div>
        <div className="space-y-1">
          <p className="text-sm text-gray-500">Order Status:</p>
          <span
            className={cn(
              "inline-flex items-center rounded-full px-2.5 py-1 text-xs font-medium ring-1 ring-inset",
              statusColors[order.pickDropStatus as keyof typeof statusColors] ||
                "bg-gray-100 text-gray-700 ring-gray-600/20"
            )}
          >
            {order.pickDropStatus}
          </span>
        </div>
      </div>

      {/* Order Content */}
      <div className="grid grid-cols-1 md:grid-cols-12 gap-6">
        {/* Left column - Item details */}
        <div className="md:col-span-4 space-y-4">
          <div className="aspect-square relative rounded-md overflow-hidden border bg-gray-50">
            {order.itemImage ? (
              <Image
                src={order.itemImage.formats.medium?.url || order.itemImage.url}
                alt={order.itemDescription}
                fill
                className="object-cover"
              />
            ) : (
              <div className="flex items-center justify-center h-full">
                <Package className="h-16 w-16 text-gray-300" />
              </div>
            )}
          </div>
          <div>
            <h4 className="font-medium">Item Description</h4>
            <p className="text-gray-600 text-sm mt-1">{order.itemDescription}</p>
          </div>
          {order.itemWeightKg && (
            <div>
              <h4 className="text-sm text-gray-500">Weight</h4>
              <p className="font-medium">{order.itemWeightKg} kg</p>
            </div>
          )}
        </div>

        {/* Right column - Delivery details */}
        <div className="md:col-span-8 space-y-5">
          {/* Delivery type and price */}
          <div className="flex justify-between items-start pb-3 border-b">
            <div>
              <h4 className="font-medium">Delivery Type</h4>
              <p className="text-gray-600 text-sm">{order.deliveryType}</p>
              {order.scheduledDateTime && (
                <p className="text-xs text-gray-500 mt-1">
                  Scheduled for: {formatDate(new Date(order.scheduledDateTime))}
                </p>
              )}
            </div>
            <div className="text-right">
              <h4 className="font-medium">Total Price</h4>
              <p className="text-lg font-bold text-green-700">${order.totalAmount.toFixed(2)}</p>
              <p className="text-xs text-gray-500">(Delivery fee: ${order.deliveryFee.toFixed(2)})</p>
            </div>
          </div>

          {/* Pickup and dropoff details */}
          <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
            {/* Pickup details */}
            <div className="bg-blue-50 p-3 rounded-lg space-y-3">
              <h4 className="font-medium flex items-center gap-1 text-blue-800">
                <MapPin size={16} /> Pickup Details
              </h4>

              <div className="space-y-2">
                <div className="flex items-start gap-2">
                  <User size={16} className="text-gray-500 mt-0.5" />
                  <div>
                    <p className="text-sm font-medium">{order.senderName}</p>
                    <p className="text-xs text-gray-600">{order.senderPhoneNumber}</p>
                  </div>
                </div>

                <div className="flex items-start gap-2">
                  <MapPin size={16} className="text-gray-500 mt-0.5" />
                  <div>
                    <p className="text-sm">{order.pickupLocation}</p>
                    <p className="text-xs text-gray-600">{order.pickupAddress}</p>
                    {order.senderAddressLine1 && <p className="text-xs text-gray-600">{order.senderAddressLine1}</p>}
                    {order.senderAddressLine2 && <p className="text-xs text-gray-600">{order.senderAddressLine2}</p>}
                  </div>
                </div>

                <div className="flex items-start gap-2">
                  <Calendar size={16} className="text-gray-500 mt-0.5" />
                  <div>
                    <p className="text-sm">{pickupDate}</p>
                  </div>
                </div>
              </div>
            </div>

            {/* Dropoff details */}
            <div className="bg-green-50 p-3 rounded-lg space-y-3">
              <h4 className="font-medium flex items-center gap-1 text-green-800">
                <MapPin size={16} /> Drop-off Details
              </h4>

              <div className="space-y-2">
                <div className="flex items-start gap-2">
                  <User size={16} className="text-gray-500 mt-0.5" />
                  <div>
                    <p className="text-sm font-medium">{order.receiverName}</p>
                    <p className="text-xs text-gray-600">{order.receiverPhoneNumber}</p>
                  </div>
                </div>

                <div className="flex items-start gap-2">
                  <MapPin size={16} className="text-gray-500 mt-0.5" />
                  <div>
                    <p className="text-sm">{order.dropOffLocation}</p>
                    <p className="text-xs text-gray-600">{order.dropOffAddress}</p>
                    {order.receiverAddressLine1 && (
                      <p className="text-xs text-gray-600">{order.receiverAddressLine1}</p>
                    )}
                    {order.receiverAddressLine2 && (
                      <p className="text-xs text-gray-600">{order.receiverAddressLine2}</p>
                    )}
                  </div>
                </div>

                <div className="flex items-start gap-2">
                  <Calendar size={16} className="text-gray-500 mt-0.5" />
                  <div>
                    <p className="text-sm">{dropOffDate}</p>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Order date and tracking info */}
          <div className="flex flex-col sm:flex-row justify-between text-sm pt-2 border-t">
            <div className="text-gray-500">Order placed: {formatDate(new Date(order.createdAt))}</div>

            {/* Add tracking button or info if available */}
            {/* {order.pickDropStatus !== "Pending" && order.pickDropStatus !== "Cancelled" && (
              <button className="mt-2 sm:mt-0 text-blue-600 hover:text-blue-800 font-medium">Track Order</button>
            )} */}

            {/* Add cancel button if order is still pending */}
            {/* {order.pickDropStatus === "Pending" && (
              <button className="mt-2 sm:mt-0 text-red-600 hover:text-red-800 font-medium">Cancel Order</button>
            )} */}
          </div>
        </div>
      </div>
    </div>
  );
};

export default PickDropOrder;
