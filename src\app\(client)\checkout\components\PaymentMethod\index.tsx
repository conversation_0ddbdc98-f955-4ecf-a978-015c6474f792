"use client";

import React, { useState } from "react";
import { CreditCard, Wallet } from "lucide-react";
import { Control } from "react-hook-form";

import { FormControl, FormField, FormItem, FormMessage } from "@/components/ui/form";
import { CheckoutFormValues } from "@/lib/schemas/checkoutFormSchema";
import { cn } from "@/lib/utils";

interface PaymentMethodProps {
  control: Control<CheckoutFormValues>;
}

function PaymentMethod({ control }: PaymentMethodProps) {
  // Local state to track the selected payment method
  const [selectedMethod, setSelectedMethod] = useState<string>("");

  return (
    <div className="w-full border rounded-lg p-5 tracking-tight space-y-5">
      {/* header */}
      <div className="mb-8 flex justify-between items-end">
        <div className="space-y-1">
          <h3 className="font-bold text-xl">Payment Method</h3>
          <p className="text-sm text-gray-400 tracking-normal">Please select your payment method</p>
        </div>

        <p className="text-gray-400 text-sm">Step 3 of 4</p>
      </div>
      {/* header ends */}

      {/* body */}
      <FormField
        control={control}
        name="paymentMethod"
        render={({ field }) => {
          // Update local state when form field changes
          if (field.value && field.value !== selectedMethod) {
            setSelectedMethod(field.value);
          }

          return (
            <FormItem>
              <FormControl>
                <div className="space-y-4">
                  {/* Credit/Debit Card Option */}
                  <div
                    className={cn(
                      "rounded-lg border p-4 cursor-pointer hover:border-gray-400 transition-colors",
                      field.value === "card" && "bg-gray-50 border-gray-500"
                    )}
                    onClick={() => {
                      // Update both the form field and local state
                      field.onChange("card");
                      setSelectedMethod("card");
                    }}
                  >
                    <div className="flex items-start space-x-3">
                      {/* CUSTOM RADIO BUTTON START */}
                      {/* Container for the radio button */}
                      <div className="relative flex h-4 w-4 items-center justify-center mt-1">
                        {/* Outer circle of the radio button */}
                        <div
                          className={cn(
                            "h-4 w-4 rounded-full border border-gray-300",
                            // When selected, make the border thicker and black
                            field.value === "card" && "border-2 border-black"
                          )}
                        >
                          {/* Inner dot that appears when selected */}
                          {field.value === "card" && (
                            <div className="absolute inset-0 flex items-center justify-center">
                              <div className="h-2 w-2 rounded-full bg-black"></div>
                            </div>
                          )}
                        </div>
                      </div>
                      {/* CUSTOM RADIO BUTTON END */}

                      <div className="flex-1">
                        <div className="flex items-center space-x-2">
                          <CreditCard className="h-5 w-5 text-gray-600" />
                          <label htmlFor="payment-card" className="font-medium text-base cursor-pointer">
                            Credit/Debit Card
                          </label>
                        </div>
                        <p className="text-sm text-gray-500 mt-1">Pay securely with your credit or debit card.</p>
                      </div>
                    </div>
                  </div>

                  {/* Cash on Delivery Option */}
                  <div
                    className={cn(
                      "rounded-lg border p-4 cursor-pointer hover:border-gray-400 transition-colors",
                      field.value === "cash_on_delivery" && "bg-gray-50 border-gray-500"
                    )}
                    onClick={() => {
                      // Update both the form field and local state
                      field.onChange("cash_on_delivery");
                      setSelectedMethod("cash_on_delivery");
                    }}
                  >
                    <div className="flex items-start space-x-3">
                      {/* CUSTOM RADIO BUTTON START */}
                      {/* Container for the radio button */}
                      <div className="relative flex h-4 w-4 items-center justify-center mt-1">
                        {/* Outer circle of the radio button */}
                        <div
                          className={cn(
                            "h-4 w-4 rounded-full border border-gray-300",
                            // When selected, make the border thicker and black
                            field.value === "cash_on_delivery" && "border-2 border-black"
                          )}
                        >
                          {/* Inner dot that appears when selected */}
                          {field.value === "cash_on_delivery" && (
                            <div className="absolute inset-0 flex items-center justify-center">
                              <div className="h-2 w-2 rounded-full bg-black"></div>
                            </div>
                          )}
                        </div>
                      </div>
                      {/* CUSTOM RADIO BUTTON END */}

                      <div className="flex-1">
                        <div className="flex items-center space-x-2">
                          <Wallet className="h-5 w-5 text-gray-600" />
                          <label htmlFor="payment-cash" className="font-medium text-base cursor-pointer">
                            Cash on Delivery
                          </label>
                        </div>
                        <p className="text-sm text-gray-500 mt-1">Pay with cash when your order is delivered.</p>
                      </div>
                    </div>
                  </div>
                </div>
              </FormControl>
              <FormMessage />
            </FormItem>
          );
        }}
      />
    </div>
  );
}

export default PaymentMethod;
