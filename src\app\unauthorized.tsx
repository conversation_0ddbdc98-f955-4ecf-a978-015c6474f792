import React from "react";
import Link from "next/link";
import { ArrowLeft } from "lucide-react";

import { <PERSON><PERSON> } from "@/components/ui/button";

export default function UnauthorizedPage() {
  return (
    <div className="flex flex-col items-center justify-center min-h-[70vh] p-6 text-center">
      <h1 className="text-3xl font-bold mb-4">401 - Unauthorized</h1>
      <p className="text-gray-600 mb-8 max-w-md">
        You need to be logged in to access this page. Please log in to continue.
      </p>
      <div className="flex flex-col sm:flex-row gap-4">
        <Button asChild variant="outline">
          <Link href="/" className="flex items-center gap-2">
            <ArrowLeft size={16} />
            Back to Home
          </Link>
        </Button>
        <Button asChild>
          <Link href="/login">Log In</Link>
        </Button>
      </div>
    </div>
  );
}
