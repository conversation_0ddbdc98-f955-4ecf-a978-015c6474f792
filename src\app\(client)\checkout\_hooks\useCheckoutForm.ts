"use client";

import { useState } from "react";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import { toast } from "sonner";

import { checkoutFormSchema, CheckoutFormValues, defaultCheckoutValues } from "@/lib/schemas/checkoutFormSchema";

export interface UseCheckoutFormProps {
  onSubmit: (data: CheckoutFormValues) => Promise<void>;
}

export const useCheckoutForm = ({ onSubmit }: UseCheckoutFormProps) => {
  const [isSubmitting, setIsSubmitting] = useState(false);

  const form = useForm<CheckoutFormValues>({
    resolver: zodResolver(checkoutFormSchema),
    defaultValues: defaultCheckoutValues,
    mode: "onChange",
  });

  const handleSubmit = async (data: CheckoutFormValues) => {
    try {
      setIsSubmitting(true);
      await onSubmit(data);
    } catch {
      toast.error("Form submission error. Please try again.");
    } finally {
      setIsSubmitting(false);
    }
  };

  return {
    form,
    isSubmitting,
    handleSubmit: form.handleSubmit(handleSubmit),
  };
};
