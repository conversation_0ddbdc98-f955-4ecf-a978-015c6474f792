import type { <PERSON>ada<PERSON> } from "next";
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>_<PERSON><PERSON>, Plus_Jakarta_Sans } from "next/font/google";
import Script from "next/script";

import "./globals.css";

import { Toaster } from "@/components/ui/sonner";
import TanstackProvider from "@/providers/TanstackProvider";

const geistSans = Geist({
  variable: "--font-geist-sans",
  subsets: ["latin"],
});

const geistMono = Geist_Mono({
  variable: "--font-geist-mono",
  subsets: ["latin"],
});

const plusJakartaSans = Plus_Jakarta_Sans({
  variable: "--font-plus-jakarta-sans",
  subsets: ["latin"],
});

export const metadata: Metadata = {
  manifest: "/manifest.json",
  title: "EEF Express",
  description: "Empowering your shopping and delivery needs, effortlessly",
  keywords: [
    "EEF Express",
    "eef",
    "eef express",
    "eefexpress",
    "eefshop",
    "eef delivery",
    "online delivery",
    "shopping",
    "delivery",
    "express delivery",
    "online shopping",
    "fast delivery",
    "dubai delivery",
    "UAE delivery",
    "delivery service",
  ],
  openGraph: {
    title: "EEF Express - Effortless Shopping & Delivery",
    description: "EEF Express empowers your shopping and delivery needs with speed, reliability, and convenience.",
    url: "https://eef-express.shop",
    siteName: "EEF Express",
    images: [
      {
        url: "https://i.ibb.co/39rK2dcR/og-image.png",
        width: 1200,
        height: 630,
        alt: "EEF Express - Effortless Shopping & Delivery",
      },
    ],
    locale: "en_US",
    type: "website",
  },
  twitter: {
    card: "summary_large_image",
    title: "EEF Express - Effortless Shopping & Delivery",
    description: "EEF Express empowers your shopping and delivery needs with speed, reliability, and convenience.",
    images: ["https://i.ibb.co/39rK2dcR/og-image.png"],
  },
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en">
      <head>
        <meta name="viewport" content="width=device-width, initial-scale=1, viewport-fit=cover" />
        <meta name="apple-mobile-web-app-capable" content="yes" />
        <meta name="apple-mobile-web-app-status-bar-style" content="black-translucent" />
      </head>
      <body className={`${plusJakartaSans.variable} ${geistSans.variable} ${geistMono.variable} antialiased`}>
        <TanstackProvider>{children}</TanstackProvider>
        <Toaster />

        {/* Tawk.to Chat Widget */}
        <Script strategy="afterInteractive" id="tawk-script">
          {`
           var Tawk_API=Tawk_API||{}, Tawk_LoadStart=new Date();
          (function(){
          var s1=document.createElement("script"),s0=document.getElementsByTagName("script")[0];
          s1.async=true;
          s1.src='https://embed.tawk.to/683cbfb263a030190c8ec73b/1ismkpkv7';
          s1.charset='UTF-8';
          s1.setAttribute('crossorigin','*');
          s0.parentNode.insertBefore(s1,s0);
          })();
          `}
        </Script>
      </body>
    </html>
  );
}
