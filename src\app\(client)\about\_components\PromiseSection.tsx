import { CheckCircle, Heart, Shield, Zap } from "lucide-react";

const promiseValues = [
  {
    icon: Zap,
    title: "Speed",
    description: "Fast delivery and quick service",
    bgColor: "bg-yellow-100",
    iconColor: "text-yellow-600",
  },
  {
    icon: Shield,
    title: "Security",
    description: "Safe and secure transactions",
    bgColor: "bg-green-100",
    iconColor: "text-green-600",
  },
  {
    icon: Heart,
    title: "Customer Satisfaction",
    description: "Your happiness is our priority",
    bgColor: "bg-red-100",
    iconColor: "text-red-600",
  },
];

export default function PromiseSection() {
  return (
    <section className="mb-16">
      <div className="flex items-center gap-4 mb-8">
        <div className="flex items-center justify-center w-12 h-12 bg-emerald-100 rounded-xl">
          <CheckCircle className="w-6 h-6 text-emerald-600" />
        </div>
        <h2 className="section-title text-gray-900">🤝 Our Promise</h2>
      </div>

      <p className="text-gray-700 leading-relaxed text-lg mb-8">
        We value speed, security, and customer satisfaction. That's why we work with trained professionals, verified
        vendors, and reliable delivery partners to ensure you always receive the best service — wherever you are in the
        UAE.
      </p>

      <div className="grid md:grid-cols-3 gap-6">
        {promiseValues.map((value, index) => {
          const IconComponent = value.icon;
          return (
            <div key={index} className="text-center border p-8 rounded-xl">
              <div className={`w-12 h-12 ${value.bgColor} rounded-full flex items-center justify-center mx-auto mb-4`}>
                <IconComponent className={`w-6 h-6 ${value.iconColor}`} />
              </div>
              <h3 className="font-semibold text-gray-900 mb-2">{value.title}</h3>
              <p className="text-gray-600 text-sm">{value.description}</p>
            </div>
          );
        })}
      </div>
    </section>
  );
}
