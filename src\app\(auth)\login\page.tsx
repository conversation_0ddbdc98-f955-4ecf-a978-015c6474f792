import React, { Suspense } from "react";
import Image from "next/image";

import LoginContent from "../_components/LoginContent";
import LoginLoading from "../_components/LoginLoading";

export default function LoginPage() {
  return (
    <div className="flex items-center flex-col gap-4 justify-center w-full p-6 pt-12 lg:h-screen">
      <Image src="/assets/svgs/logo.svg" width={100} height={80} alt="eefexpress" />
      <Suspense fallback={<LoginLoading />}>
        <LoginContent />
      </Suspense>
    </div>
  );
}
