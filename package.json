{"name": "eefexpress", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "eslint \"src/**/*.+(ts|tsx)\"", "lint:fix": "eslint \"src/**/*.+(ts|tsx)\" --fix", "format": "prettier . --write", "format:check": "prettier . --check", "prepare": "husky"}, "lint-staged": {"**/*.{ts,tsx}": ["eslint --fix", "prettier --write"]}, "dependencies": {"@ducanh2912/next-pwa": "^10.2.9", "@hookform/resolvers": "^5.0.1", "@radix-ui/react-accordion": "^1.2.11", "@radix-ui/react-checkbox": "^1.3.1", "@radix-ui/react-dialog": "^1.1.13", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-label": "^2.1.6", "@radix-ui/react-popover": "^1.1.13", "@radix-ui/react-radio-group": "^1.3.6", "@radix-ui/react-scroll-area": "^1.2.9", "@radix-ui/react-select": "^2.2.4", "@radix-ui/react-slider": "^1.3.5", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-tooltip": "^1.2.6", "@tanstack/react-query": "^5.76.1", "@tanstack/react-query-devtools": "^5.76.1", "axios": "^1.9.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "date-fns": "^4.1.0", "embla-carousel-react": "^8.6.0", "install": "^0.13.0", "js-cookie": "^3.0.5", "lucide-react": "^0.509.0", "next": "15.3.2", "next-themes": "^0.4.6", "npm": "^11.3.0", "react": "^19.0.0", "react-day-picker": "^8.10.1", "react-dom": "^19.0.0", "react-hook-form": "^7.56.3", "sonner": "^2.0.3", "swiper": "^11.2.6", "tailwind-merge": "^3.3.0", "zod": "^3.24.4"}, "devDependencies": {"@eslint/eslintrc": "^3.2.0", "@eslint/js": "^9.18.0", "@ianvs/prettier-plugin-sort-imports": "^4.4.0", "@tailwindcss/postcss": "^4", "@types/js-cookie": "^3.0.6", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "@typescript-eslint/eslint-plugin": "^8.19.1", "@typescript-eslint/parser": "^8.19.1", "eslint": "^9.18.0", "eslint-config-next": "^15.1.4", "eslint-config-prettier": "^9.1.0", "eslint-plugin-prettier": "^5.2.1", "husky": "^9.1.7", "lint-staged": "^16.0.0", "prettier": "^3.4.2", "prettier-plugin-sort-json": "^4.1.1", "tailwindcss": "^4", "tw-animate-css": "^1.2.9", "typescript": "^5"}, "resolutions": {"lightningcss": "^1.23.0"}}