import { CartData } from "@/types/cart";
import { apiRequest } from "@/utils/axios-utils";

export interface AddToCartResponse {
  data: CartData;
  meta: null;
}

export const addToCart = async (productId: number, quantity: number) => {
  const response = await apiRequest<AddToCartResponse>({
    url: "/cart/items",
    method: "POST",
    data: {
      productId: productId,
      quantity: quantity,
    },
    showToast: false,
  });
  return response;
};
