"use client";

import React, { useState } from "react";
import Image from "next/image";
import Link from "next/link";
import { StarIcon } from "lucide-react";

import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";
import { getApiImage } from "@/utils/getApiImage";
import { smartTruncate } from "@/utils/smartTruncate";
import ProductCardActionButtons from "../ProductCardActionButtons";
import { Badge } from "../ui/badge";

interface ProductCardProps {
  _id: number;
  name: string;
  image: string;
  price: number;
  stock?: number;
  ratings?: number;
}

function ProductCard({ _id, name, image, price, stock, ratings }: ProductCardProps) {
  const [isHovered, setIsHovered] = useState(false);
  const isTruncated = name.length > 75;
  const displayName = smartTruncate(name, 75);

  return (
    <div
      className="group relative overflow-hidden group"
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
    >
      <Link href={`/product/${_id}`}>
        {/* Image Container */}
        <div className="relative h-[13rem] overflow-hidden p-4 bg-gray-50 rounded-xl flex items-center justify-center">
          <Image
            width={140}
            height={100}
            alt={name}
            src={getApiImage(image)}
            style={{ objectFit: "contain" }}
            className="rounded-sm aspect-square group-hover:scale-110 transition-all duration-300"
          />

          {/* on show Hover Action Buttons */}
          <ProductCardActionButtons stock={stock!} productId={_id} isHovered={isHovered} />
          <div className="absolute bottom-0 left-0 px-4 flex items-center gap-3 justify-between w-full mb-2">
            {(stock ?? 0) > 0 ? (
              <Badge variant={"outline"} className="bg-white rounded-full">
                {stock ?? 0} in-stock
              </Badge>
            ) : (
              <Badge variant={"destructive"}>out of stock</Badge>
            )}
            <Badge variant={"outline"} className="bg-white rounded-full">
              <StarIcon stroke="none" fill="#000" /> {ratings?.toFixed(1)}
            </Badge>
          </div>
        </div>

        {/* Product Info */}
        <div className="mt-3 space-y-1 ">
          {isTruncated ? (
            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <h4 className="text-sm md:text-base text-gray-700 group-hover:text-black cursor-help">
                    {displayName}
                  </h4>
                </TooltipTrigger>
                <TooltipContent
                  side="bottom"
                  className="max-w-xs text-sm p-3 bg-gray-900 text-white rounded-lg shadow-lg"
                >
                  <p className="leading-relaxed">{name}</p>
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>
          ) : (
            <h4 className="text-sm md:text-base text-gray-700 group-hover:text-black">{displayName}</h4>
          )}
          <p className="text-md font-semibold text-primary">
            {price} {price && "AED"}
          </p>
        </div>
      </Link>
    </div>
  );
}

export default ProductCard;
