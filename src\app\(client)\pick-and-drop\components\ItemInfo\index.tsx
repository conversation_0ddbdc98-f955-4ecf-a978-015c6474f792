"use client";

import React, { useState } from "react";
import Image from "next/image";
import { UploadCloud, X } from "lucide-react";
import { Control } from "react-hook-form";

import { Button } from "@/components/ui/button";
import { FormControl, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { PickDropFormValues } from "@/lib/schemas/pickDropFormSchema";

interface ItemInfoProps {
  control: Control<PickDropFormValues>;
}

function ItemInfo({ control }: ItemInfoProps) {
  const [imagePreview, setImagePreview] = useState<string | null>(null);

  const handleImageChange = (e: React.ChangeEvent<HTMLInputElement>, onChange: (file: File | null) => void) => {
    if (e.target.files && e.target.files[0]) {
      const selectedFile = e.target.files[0];
      onChange(selectedFile);

      // Create preview URL
      const previewUrl = URL.createObjectURL(selectedFile);
      setImagePreview(previewUrl);
    }
  };

  const handleDragOver = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
  };

  const handleDrop = (e: React.DragEvent<HTMLDivElement>, onChange: (file: File | null) => void) => {
    e.preventDefault();

    if (e.dataTransfer.files && e.dataTransfer.files[0]) {
      const droppedFile = e.dataTransfer.files[0];
      onChange(droppedFile);

      // Create preview URL
      const previewUrl = URL.createObjectURL(droppedFile);
      setImagePreview(previewUrl);
    }
  };

  const handleRemoveImage = (onChange: (file: File | null) => void) => {
    setImagePreview(null);
    onChange(null);

    // Reset the file input value
    const imageInput = document.getElementById("imageInput") as HTMLInputElement | null;
    if (imageInput) {
      imageInput.value = ""; // Reset the file input
    }
  };

  return (
    <div className="w-full border rounded-lg p-5 tracking-tight space-y-5">
      {/* header */}
      <div className="mb-3">
        <h3 className="font-bold text-lg">Item Info</h3>
        <p className="text-sm text-gray-400 tracking-normal">Please enter information about item</p>
      </div>

      {/* body */}
      <div className="space-y-5">
        {/* Item description */}
        <FormField
          control={control}
          name="itemDescription"
          render={({ field }) => (
            <FormItem className="space-y-2">
              <FormLabel>Item description</FormLabel>
              <FormControl>
                <Textarea
                  id="itemDescription"
                  className="min-h-24"
                  placeholder="Enter description of item here"
                  {...field}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        {/* Item weight */}
        <FormField
          control={control}
          name="itemWeightKg"
          render={({ field }) => (
            <FormItem className="space-y-2">
              <FormLabel>Item weight (Kg)</FormLabel>
              <FormControl>
                <Input
                  id="itemWeight"
                  type="number"
                  step="0.01"
                  className="bg-gray-50"
                  placeholder="Enter item weight"
                  value={field.value === null ? "" : field.value}
                  onChange={(e) => field.onChange(e.target.value ? parseFloat(e.target.value) : null)}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        {/* Item image */}
        <FormField
          control={control}
          name="itemImage"
          render={({ field: { onChange, ...fieldProps } }) => (
            <FormItem className="space-y-2">
              <FormLabel>Item Image</FormLabel>
              <div
                className="border-2 border-dashed rounded-md p-8 text-center cursor-pointer bg-gray-50 relative overflow-hidden flex items-center justify-center"
                onDragOver={handleDragOver}
                onDrop={(e) => handleDrop(e, onChange)}
                onClick={() => document.getElementById("imageInput")?.click()}
              >
                <div className="flex flex-col items-center justify-center space-y-2">
                  <div className="h-8 w-8 bg-gray-200 rounded-full flex items-center justify-center">
                    <UploadCloud className="h-5 w-5 text-gray-500" />
                  </div>
                  <p className="text-sm">Click to upload or drag and drop</p>
                  <p className="text-xs text-gray-400">Accepted file types: jpg, jpeg, png</p>
                </div>

                <input
                  id="imageInput"
                  type="file"
                  accept=".jpg,.jpeg,.png"
                  className="hidden"
                  onChange={(e) => handleImageChange(e, onChange)}
                  {...{ ...fieldProps, value: undefined }}
                />
              </div>
              <FormMessage />
            </FormItem>
          )}
        />

        {/* Preview */}
        <div className="space-y-2">
          <Label htmlFor="preview">Preview</Label>
          <div className="border rounded-md min-h-64 h-auto w-full bg-white relative">
            {imagePreview ? (
              <>
                <div className="relative w-full h-64">
                  <Image src={imagePreview} alt="Item preview" fill className="rounded-md object-contain" />
                </div>
                <FormField
                  control={control}
                  name="itemImage"
                  render={({ field: { onChange } }) => (
                    <Button
                      type="button"
                      onClick={() => handleRemoveImage(onChange)}
                      className="absolute top-2 right-2 bg-gray-200 text-gray-700 hover:bg-gray-300 rounded-full p-1 shadow-sm"
                      size="icon"
                    >
                      <X className="h-4 w-4" />
                      <span className="sr-only">Remove image</span>
                    </Button>
                  )}
                />
              </>
            ) : (
              <div className="min-h-64 w-full flex items-center justify-center">
                <p className="text-sm text-gray-400 my-auto">No image selected</p>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}

export default ItemInfo;
