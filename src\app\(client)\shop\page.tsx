"use client";

import React, { Suspense, useEffect, useRef, useState } from "react";
import { useRouter, useSearchParams } from "next/navigation";

import useGetProductsQuery from "@/app/(client)/_hooks/useGetProductsQuery";
import PaginationWithEllipsis from "@/components/PaginationWithEllipsis";
import TopLoadingBar from "@/components/TopLoadingBar";
import { Skeleton } from "@/components/ui/skeleton";
import { ProductsFilterParams } from "@/services/products/getProducts.service";
import CategorySidebar from "./_components/CategorySidebar";
import ProductList from "./_components/ProductList";
import { filtersToURLParams, initializeFiltersFromURL } from "./_utils/filterUtils";

// Component that uses hooks requiring Suspense
function ShopContent() {
  const router = useRouter();
  const searchParams = useSearchParams();

  // Initialize filters from URL search params
  const [filters, setFilters] = useState<ProductsFilterParams>(() => initializeFiltersFromURL(searchParams!));

  // Update filters when URL search params change
  useEffect(() => {
    const newFilters = initializeFiltersFromURL(searchParams!);
    setFilters(newFilters);
  }, [searchParams]);

  const { data, isLoading, isError, isFetching } = useGetProductsQuery(filters);
  const products = data?.results || [];
  const searchQuery = filters.search;

  // Update URL when filters change (but not on initial render)
  const isInitialRender = useRef(true);

  useEffect(() => {
    if (isInitialRender.current) {
      isInitialRender.current = false;
      return;
    }

    const params = filtersToURLParams(filters);
    router.push(`/shop?${params.toString()}`, { scroll: false });
  }, [filters, router]);

  const handleCategoryClick = (categoryId: number) => {
    setFilters((prev) => ({
      ...prev,
      category: categoryId,
      // Reset to page 1 when changing category
      page: 1,
    }));
  };

  // Fix: Update the handleFilterChange function to accept partial filter objects
  const handleFilterChange = (newFilters: Partial<ProductsFilterParams>) => {
    setFilters((prev) => ({
      ...prev,
      ...newFilters,
    }));
  };

  const handlePageChange = (page: number) => {
    setFilters((prev) => ({
      ...prev,
      page,
    }));
    // Scroll to top when changing page
    window.scrollTo({ top: 0, behavior: "smooth" });
  };

  return (
    <>
      <TopLoadingBar isLoading={isFetching} />

      <div className="max-width-wrapper mb-20 ">
        {/* Search results title */}
        {searchQuery && (
          <div className="mb-6">
            <h1 className="text-2xl font-bold">Search results for "{searchQuery}"</h1>
          </div>
        )}

        <div className="grid grid-cols-1 md:grid-cols-12 gap-6 md:gap-10">
          {/* Categories sidebar with filters */}
          <CategorySidebar
            onCategoryClick={handleCategoryClick}
            selectedCategoryId={filters.category}
            filters={filters}
            onFilterChange={handleFilterChange}
          />

          {/* Products section */}
          <ProductList products={products} isLoading={isLoading} isError={isError} />
        </div>

        {/* Pagination */}
        <div className="mt-8">
          <PaginationWithEllipsis
            currentPage={filters.page || 1}
            totalPages={data?.pagination?.pageCount || 0}
            onPageChange={handlePageChange}
          />
        </div>
      </div>
    </>
  );
}

// Loading fallback
function ShopLoading() {
  return (
    <div className="max-width-wrapper mb-20 mt-5 md:mt-10">
      <div className="grid grid-cols-1 md:grid-cols-12 gap-6 md:gap-10">
        {/* Categories sidebar skeleton */}
        <div className="md:col-span-4 lg:col-span-3 space-y-6">
          <div className="border rounded-lg p-5">
            <Skeleton className="h-6 w-32 mb-4" />
            <div className="space-y-3">
              {Array(6)
                .fill(0)
                .map((_, i) => (
                  <Skeleton key={i} className="h-5 w-full" />
                ))}
            </div>
          </div>

          <div className="border rounded-lg p-5">
            <Skeleton className="h-6 w-32 mb-4" />
            <div className="space-y-4">
              {Array(3)
                .fill(0)
                .map((_, i) => (
                  <div key={i} className="space-y-2">
                    <Skeleton className="h-5 w-24" />
                    <Skeleton className="h-8 w-full" />
                  </div>
                ))}
            </div>
          </div>
        </div>

        {/* Products section skeleton */}
        <div className="md:col-span-8 lg:col-span-9 grid grid-cols-2 gap-4 lg:grid-cols-3 h-fit">
          {Array(6)
            .fill(0)
            .map((_, i) => (
              <div key={i} className="space-y-3">
                <Skeleton className="h-[13rem] w-full rounded-xl" />
                <Skeleton className="h-4 w-2/3" />
                <Skeleton className="h-4 w-1/3" />
              </div>
            ))}
        </div>
      </div>

      {/* Pagination skeleton */}
      <div className="mt-8 flex justify-center">
        <Skeleton className="h-10 w-64" />
      </div>
    </div>
  );
}

// Main component with Suspense boundary
function Shop() {
  return (
    <Suspense fallback={<ShopLoading />}>
      <ShopContent />
    </Suspense>
  );
}

export default Shop;
