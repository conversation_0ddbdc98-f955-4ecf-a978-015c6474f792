export interface PickDropImage {
  id: number;
  documentId: string;
  name: string;
  alternativeText: string | null;
  caption: string | null;
  width: number;
  height: number;
  formats: {
    large: {
      ext: string;
      url: string;
      hash: string;
      mime: string;
      name: string;
      path: string | null;
      size: number;
      width: number;
      height: number;
      sizeInBytes: number;
      provider_metadata: {
        public_id: string;
        resource_type: string;
      };
    };
    small: {
      ext: string;
      url: string;
      hash: string;
      mime: string;
      name: string;
      path: string | null;
      size: number;
      width: number;
      height: number;
      sizeInBytes: number;
      provider_metadata: {
        public_id: string;
        resource_type: string;
      };
    };
    medium: {
      ext: string;
      url: string;
      hash: string;
      mime: string;
      name: string;
      path: string | null;
      size: number;
      width: number;
      height: number;
      sizeInBytes: number;
      provider_metadata: {
        public_id: string;
        resource_type: string;
      };
    };
    thumbnail: {
      ext: string;
      url: string;
      hash: string;
      mime: string;
      name: string;
      path: string | null;
      size: number;
      width: number;
      height: number;
      sizeInBytes: number;
      provider_metadata: {
        public_id: string;
        resource_type: string;
      };
    };
  };
  hash: string;
  ext: string;
  mime: string;
  size: number;
  url: string;
  previewUrl: string | null;
  provider: string;
  provider_metadata: {
    public_id: string;
    resource_type: string;
  };
  folderPath: string;
  createdAt: string;
  updatedAt: string;
  publishedAt: string;
  locale: string | null;
}

export interface PickDropUser {
  id: number;
  documentId: string;
  username: string;
  email: string;
}

export interface PickDrop {
  id: number;
  documentId: string;
  senderName: string;
  receiverName: string;
  itemDescription: string;
  pickDropStatus: string;
  assignedRider: null | string;
  deliveryType: string;
  scheduledDateTime: string | null;
  price: number | null;
  createdAt: string;
  updatedAt: string;
  publishedAt: string;
  locale: string | null;
  subtotal: number;
  deliveryFee: number;
  totalAmount: number;
  adminNotes: string | null;
  approvedAt: string | null;
  senderAddressLine1: string;
  senderAddressLine2: string | null;
  receiverAddressLine1: string;
  receiverAddressLine2: string | null;
  senderPhoneNumber: string;
  receiverPhoneNumber: string;
  itemWeightKg: number | null;
  pickupLocation: string;
  pickupDateTime: string;
  pickupAddress: string;
  dropOffLocation: string;
  dropOffDateTime: string;
  dropOffAddress: string;
  itemImage: PickDropImage | null;
  users_permissions_user: PickDropUser;
}

export interface PickDropResponse {
  data: PickDrop;
  meta: Record<string, null>;
}

export interface PickDropPagination {
  page: number;
  pageSize: number;
  pageCount: number;
  total: number;
}

export interface PickDropSummary {
  total: number;
  pending: number;
  confirmed: number;
  inTransit: number;
  completed: number;
  cancelled: number;
}

export interface GetMyPickDropsResponse {
  data: {
    results: PickDrop[];
    pagination: PickDropPagination;
    summary: PickDropSummary;
  };
  meta: Record<string, null>;
}

export interface GetMyPickDropsParams {
  page?: number;
  pageSize?: number;
  sort?: string;
}
