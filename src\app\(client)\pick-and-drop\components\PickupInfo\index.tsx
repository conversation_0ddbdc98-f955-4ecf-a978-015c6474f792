import React from "react";
import { Control } from "react-hook-form";

import { DateTimePicker24h } from "@/components/ui/datetime-picker";
import { FormControl, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { PickDropFormValues } from "@/lib/schemas/pickDropFormSchema";

interface PickUpInfoProps {
  control: Control<PickDropFormValues>;
}

function PickUpInfo({ control }: PickUpInfoProps) {
  return (
    <div className="w-full border rounded-lg p-5 tracking-tight space-y-5">
      {/* header  */}
      <div className="mb-8 flex justify-between items-end">
        <div className="space-y-1">
          <h3 className="font-bold text-xl ">Pickup Info</h3>
          <p className="text-sm text-gray-400 tracking-normal">Please enter your pickup information</p>
        </div>

        <p className="text-gray-400 text-sm">Step 3 of 6</p>
      </div>
      {/* header ends */}

      {/* body  */}
      <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
        {/* input group  */}
        <FormField
          control={control}
          name="pickupLocation"
          render={({ field }) => (
            <FormItem className="space-y-2.5">
              <FormLabel>Location</FormLabel>
              <Select value={field.value} onValueChange={field.onChange}>
                <FormControl>
                  <SelectTrigger className="w-full h-11">
                    <SelectValue placeholder="Select a city" />
                  </SelectTrigger>
                </FormControl>
                <SelectContent>
                  <SelectItem value="Abu Dhabi">Abu Dhabi</SelectItem>
                  <SelectItem value="Dubai">Dubai</SelectItem>
                  <SelectItem value="Sharjah">Sharjah</SelectItem>
                  <SelectItem value="Ajman">Ajman</SelectItem>
                  <SelectItem value="Umm Al Quwain">Umm Al Quwain</SelectItem>
                  <SelectItem value="Ras Al Khaimah">Ras Al Khaimah</SelectItem>
                  <SelectItem value="Fujairah">Fujairah</SelectItem>
                </SelectContent>
              </Select>
              <FormMessage />
            </FormItem>
          )}
        />
        {/* input group - DateTime */}
        <FormField
          control={control}
          name="pickupDateTime"
          render={({ field }) => (
            <FormItem className="space-y-2.5">
              <FormLabel>Pickup Date & Time</FormLabel>
              <FormControl>
                <DateTimePicker24h value={field.value} onChange={field.onChange} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
      </div>

      <FormField
        control={control}
        name="pickupAddress"
        render={({ field }) => (
          <FormItem className="space-y-2.5">
            <FormLabel>Address</FormLabel>
            <FormControl>
              <Input placeholder="123 Main St" {...field} />
            </FormControl>
            <FormMessage />
          </FormItem>
        )}
      />
    </div>
  );
}

export default PickUpInfo;
