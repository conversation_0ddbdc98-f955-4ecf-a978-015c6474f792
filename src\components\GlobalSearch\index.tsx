"use client";

import React, { Suspense, useEffect, useRef, useState } from "react";
import Image from "next/image";
import Link from "next/link";
import { usePathname, useRouter, useSearchParams } from "next/navigation";
import { Search, X } from "lucide-react";

import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Sheet, SheetContent, SheetTrigger } from "@/components/ui/sheet";
import { cn } from "@/lib/utils";

function SearchContent({
  isOpen,
  setIsOpen,
  inputRef,
  className,
}: {
  isOpen: boolean;
  setIsOpen: (open: boolean) => void;
  inputRef: React.RefObject<HTMLInputElement | null>;
  className?: string;
}) {
  const router = useRouter();
  const pathname = usePathname();
  const searchParams = useSearchParams();
  const [query, setQuery] = useState("");

  // Handle search submission
  const handleSearch = (e?: React.FormEvent) => {
    if (e) e.preventDefault();

    if (query.trim()) {
      // If we're already on the shop page, preserve existing filters
      if (pathname === "/shop") {
        const currentFilters = initializeFiltersFromURL(searchParams!);
        const newParams = new URLSearchParams();

        // Add all current filters except search and page
        Object.entries(currentFilters).forEach(([key, value]) => {
          if (key !== "search" && key !== "page" && value !== undefined) {
            newParams.set(key, String(value));
          }
        });

        // Add new search query and reset to page 1
        newParams.set("search", query.trim());
        newParams.set("page", "1");
        newParams.set("pageSize", "12");

        router.push(`/shop?${newParams.toString()}`);
      } else {
        // If coming from another page, use default parameters
        router.push(`/shop?page=1&pageSize=12&search=${encodeURIComponent(query.trim())}`);
      }

      setIsOpen(false);
      setQuery("");
    }
  };

  // Focus input when search opens
  useEffect(() => {
    if (isOpen && inputRef.current) {
      inputRef.current.focus();
    }
  }, [isOpen, inputRef]);

  return (
    <Sheet open={isOpen} onOpenChange={setIsOpen}>
      <SheetTrigger asChild>
        <Button
          variant="ghost"
          size="icon"
          className={cn("h-10 w-10 rounded-full", className)}
          aria-label="Open search"
        >
          <Search className="h-4 w-4" />
        </Button>
      </SheetTrigger>
      <SheetContent side="top" className="h-20 p-0 border-b shadow-md" showCloseButton={false}>
        <div className="max-width-wrapper h-full flex items-center justify-between">
          {/* Logo */}
          <Link href="/" className="flex-shrink-0 hidden sm:flex">
            <Image src="/assets/svgs/logo.svg" width={100} height={80} alt="eefexpress" />
          </Link>

          {/* Search form */}
          <form onSubmit={handleSearch} className="flex-1 max-w-2xl mx-4 relative">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
              <Input
                ref={inputRef}
                value={query}
                onChange={(e) => setQuery(e.target.value)}
                placeholder="Search for products..."
                className="h-12 pl-10 pr-10 rounded-full border-gray-200 bg-gray-50 focus-visible:ring-blue-200/50 focus-visible:ring-2"
                autoComplete="off"
              />
              {query && (
                <Button
                  type="button"
                  variant="ghost"
                  size="icon"
                  className="absolute right-2 top-1/2 transform -translate-y-1/2 h-8 w-8"
                  onClick={() => setQuery("")}
                  aria-label="Clear search"
                >
                  <X className="h-4 w-4" />
                </Button>
              )}
            </div>
            <div className="absolute right-3 top-1/2 transform -translate-y-1/2 hidden md:flex items-center text-xs text-gray-500">
              <kbd className="px-1.5 py-0.5 bg-gray-100 rounded text-xs">Enter</kbd>
              <span className="mx-2">to search</span>
            </div>
          </form>

          {/* Close button */}
          <Button
            variant="ghost"
            size="icon"
            className="flex-shrink-0 h-10 w-10 rounded-full"
            onClick={() => setIsOpen(false)}
            aria-label="Close search"
          >
            <X className="h-5 w-5" />
          </Button>
        </div>
      </SheetContent>
    </Sheet>
  );
}

// Loading fallback
function SearchLoading({ className }: { className?: string }) {
  return (
    <Button variant="ghost" size="icon" className={cn("h-10 w-10 rounded-full", className)} disabled>
      <Search className="h-4 w-4" />
    </Button>
  );
}

// Main component with Suspense boundary
const GlobalSearch = ({ className }: { className?: string }) => {
  const [isOpen, setIsOpen] = useState(false);
  const inputRef = useRef<HTMLInputElement>(null);

  // Handle escape key to close search
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if ((e.ctrlKey || e.metaKey) && e.key === "k") {
        e.preventDefault();
        setIsOpen(true);
      }
    };

    window.addEventListener("keydown", handleKeyDown);
    return () => window.removeEventListener("keydown", handleKeyDown);
  }, []);

  // Reset state when sheet closes
  const handleOpenChange = (open: boolean) => {
    setIsOpen(open);
    if (!open) {
      // No need to reset query here as it's handled in the SearchContent component
    }
  };

  return (
    <Suspense fallback={<SearchLoading className={className} />}>
      <SearchContent isOpen={isOpen} setIsOpen={handleOpenChange} inputRef={inputRef} className={className} />
    </Suspense>
  );
};

export default GlobalSearch;

// Helper function to initialize filters from URL
function initializeFiltersFromURL(searchParams: URLSearchParams) {
  const filters: Record<string, string | number | undefined> = {};

  // Extract search parameters
  filters.search = searchParams.get("search") || undefined;
  filters.page = Number(searchParams.get("page")) || 1;
  filters.pageSize = Number(searchParams.get("pageSize")) || 12;

  // Add other filters as needed

  return filters;
}
