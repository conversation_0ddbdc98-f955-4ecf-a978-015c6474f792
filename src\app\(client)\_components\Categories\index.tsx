"use client";

import React from "react";
import { Autoplay, Navigation, Pagination } from "swiper/modules";
import { Swiper, SwiperSlide } from "swiper/react";

import { Skeleton } from "@/components/ui/skeleton";
import { useGetCategoriesWithCountQuery } from "../../_hooks/useGetCategoriesWithCountQuery";
import CategoryCard from "../CategoryCard";

import "swiper/css";
import "swiper/css/pagination";
import "swiper/css/navigation";

function Categories() {
  const { data: categories, isLoading, isError } = useGetCategoriesWithCountQuery();

  return (
    <section className="section">
      {/* section title */}
      <div className="mb-10 text-center">
        <small className="text-gray-500 text-200t text-xs">SHOP BY CATEGORIES</small>
        <h3 className="section-title">Categories</h3>
      </div>

      {/* Loading state */}
      {isLoading && (
        <div className="grid gap-6 sm:grid-cols-2 lg:grid-cols-3">
          {Array(3)
            .fill(0)
            .map((_, index) => (
              <div key={index} className="space-y-3">
                <Skeleton className="h-[14rem] w-full rounded-xl" />
              </div>
            ))}
        </div>
      )}

      {/* Error state */}
      {isError && (
        <div className="text-center py-10">
          <p className="text-red-500">Failed to load categories. Please try again later.</p>
        </div>
      )}

      {/* Category list with Swiper */}
      {!isLoading && !isError && categories && (
        <Swiper
          slidesPerView={1}
          spaceBetween={20}
          pagination={{
            clickable: true,
          }}
          breakpoints={{
            300: {
              slidesPerView: 1.3,
              spaceBetween: 20,
            },
            640: {
              slidesPerView: 2.15,
              spaceBetween: 20,
            },
            // When window width is >= 1024px (lg)
            1024: {
              slidesPerView: 3,
              spaceBetween: 30,
            },
          }}
          autoplay={{
            delay: 4000,
            disableOnInteraction: false,
          }}
          modules={[Autoplay, Pagination, Navigation]}
          className="mySwiper"
        >
          {categories.map((category) => (
            <SwiperSlide key={category.id}>
              <CategoryCard
                id={category.id}
                title={category.name}
                image={category?.image?.url || "/assets/images/placeholder.png"}
              />
            </SwiperSlide>
          ))}
        </Swiper>
      )}
    </section>
  );
}

export default Categories;
