"use client";

import React from "react";

import ProductCard from "@/components/ProductCard";
import { Skeleton } from "@/components/ui/skeleton";
import { Product } from "@/types/product";
import useGetNewestProductsQuery from "../../_hooks/useGetNewestProductsQuery";

function LatestProducts() {
  const { data: products, isLoading, isError } = useGetNewestProductsQuery();

  return (
    <div className="section">
      {/* section title */}
      <div className="mb-10 text-center">
        <small className="text-textGray text-200">NEW ARRIVALS</small>
        <h3 className="section-title">Our latest Products</h3>
      </div>

      {/* products list */}
      <div className="grid grid-cols-2 gap-4 gap-y-8 lg:grid-cols-3 xl:grid-cols-4">
        {isLoading &&
          // Loading skeletons
          Array(8)
            .fill(0)
            .map((_, index) => (
              <div key={index} className="space-y-3">
                <Skeleton className="h-[13rem] w-full rounded-xl" />
                <Skeleton className="h-4 w-2/3" />
                <Skeleton className="h-4 w-1/3" />
              </div>
            ))}

        {isError && (
          <div className="col-span-full text-center py-10">
            <p className="text-red-500">Failed to load products. Please try again later.</p>
          </div>
        )}

        {!isLoading &&
          !isError &&
          products?.map((product: Product) => (
            <ProductCard
              key={product.id}
              name={product.name}
              image={product.images?.[0]?.url || "/assets/images/placeholder-img.png"}
              price={product.price}
              stock={product.stock}
              ratings={product.ratings}
              _id={product.id}
            />
          ))}
      </div>
    </div>
  );
}

export default LatestProducts;
