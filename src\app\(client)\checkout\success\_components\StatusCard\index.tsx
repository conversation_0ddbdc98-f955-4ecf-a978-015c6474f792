import React from "react";

interface StatusCardProps {
  icon: React.ReactNode;
  title: string;
  description: string;
  active: boolean;
}

function StatusCard({ icon, title, description, active }: StatusCardProps) {
  return (
    <div className={`border rounded-lg p-4 text-center ${active ? "border-green-500 bg-green-50" : "border-gray-200"}`}>
      <div
        className={`mx-auto w-12 h-12 flex items-center justify-center rounded-full mb-3 ${active ? "bg-green-100 text-green-600" : "bg-gray-100 text-gray-400"}`}
      >
        {icon}
      </div>
      <h3 className={`font-medium mb-1 ${active ? "text-green-700" : "text-gray-500"}`}>{title}</h3>
      <p className="text-sm text-gray-500">{description}</p>
    </div>
  );
}

export default StatusCard;
