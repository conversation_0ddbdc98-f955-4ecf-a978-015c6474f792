import { PickDropResponse } from "@/types/pickDrop";
import { apiRequest } from "@/utils/axios-utils";

export const createPickDrop = async (formData: FormData): Promise<PickDropResponse> => {
  const response = await apiRequest<PickDropResponse>({
    url: "/pick-drops",
    method: "POST",
    data: formData,
    headers: {
      "Content-Type": "multipart/form-data",
    },
    showToast: true,
  });

  return response;
};
