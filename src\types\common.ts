export interface Children {
  children: React.ReactNode;
}

export interface SocialIcon {
  icon: string;
  link: string;
}
export interface NavLink {
  link: string;
  text: string;
}

export interface FooterLink {
  text: string;
  link: string;
  outLink?: string;
}

export interface FooterSection {
  title: string;
  links: FooterLink[];
}

export interface BannerSlide {
  title: string;
  description?: string;
  ctaText: string;
  ctaLink: string;
  image: string;
}
