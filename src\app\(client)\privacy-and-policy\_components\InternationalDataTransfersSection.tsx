import { Globe } from "lucide-react";

export default function InternationalDataTransfersSection() {
  return (
    <section className="mb-16">
      <div className="flex items-center gap-4 mb-8">
        <div className="flex items-center justify-center w-12 h-12 bg-cyan-100 rounded-xl">
          <Globe className="w-6 h-6 text-cyan-600" />
        </div>
        <h2 className="text-3xl font-bold text-gray-900">8. International Data Transfers</h2>
      </div>
      <div className="bg-cyan-50 border border-cyan-200 rounded-xl p-6">
        <p className="text-cyan-800 leading-relaxed">
          While our services are UAE-based, if any data is processed outside the UAE, we ensure appropriate data protection safeguards in accordance with UAE regulations.
        </p>
      </div>
    </section>
  );
}
