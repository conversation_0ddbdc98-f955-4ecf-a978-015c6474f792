import { Eye, FileText, Globe, Lock, Mail, MapPin, Phone, Shield, Users } from "lucide-react";

export default function PrivacyAndPolicy() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 via-white to-blue-50">
      {/* Hero Section */}
      <div className="bg-gradient-to-r from-blue-600 via-purple-600 to-indigo-700 text-white">
        <div className="max-width-wrapper py-16 lg:py-24">
          <div className="text-center">
            <div className="inline-flex items-center justify-center w-16 h-16 bg-white/20 rounded-full mb-6 backdrop-blur-sm">
              <Shield className="w-8 h-8" />
            </div>
            <h1 className="text-4xl lg:text-6xl font-bold mb-4 tracking-tight">🔒 Privacy Policy</h1>
            <p className="text-xl lg:text-2xl text-blue-100 max-w-3xl mx-auto leading-relaxed">
              <strong>EEF EXPRESS</strong> - Your privacy is important to us. This Privacy Policy explains how we
              collect, use, store, and protect your personal information.
            </p>
            <div className="mt-8 text-sm text-blue-200">
              We are committed to complying with the UAE's Personal Data Protection Law and applicable international
              data privacy standards.
            </div>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="max-width-wrapper py-12 lg:py-16">
        <div className="max-w-4xl mx-auto">
          {/* Section 1: Information We Collect */}
          <section className="mb-16">
            <div className="flex items-center gap-4 mb-8">
              <div className="flex items-center justify-center w-12 h-12 bg-blue-100 rounded-xl">
                <FileText className="w-6 h-6 text-blue-600" />
              </div>
              <h2 className="text-3xl font-bold text-gray-900">1. Information We Collect</h2>
            </div>
            <p className="text-lg text-gray-700 mb-8 leading-relaxed">
              When you register or use EEF EXPRESS, we may collect the following types of information:
            </p>

            <div className="grid md:grid-cols-2 gap-6 mb-8">
              <div className="bg-white rounded-2xl p-6 shadow-lg border border-gray-100 hover:shadow-xl transition-shadow">
                <div className="flex items-center gap-3 mb-4">
                  <Users className="w-6 h-6 text-blue-600" />
                  <h3 className="text-xl font-semibold text-gray-900">🧾 Personal Information</h3>
                </div>
                <ul className="space-y-2 text-gray-700">
                  <li className="flex items-center gap-2">
                    <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                    Full Name
                  </li>
                  <li className="flex items-center gap-2">
                    <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                    Contact Number
                  </li>
                  <li className="flex items-center gap-2">
                    <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                    Email Address
                  </li>
                  <li className="flex items-center gap-2">
                    <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                    Delivery Address
                  </li>
                  <li className="flex items-center gap-2">
                    <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                    Payment Information (via secure third-party gateways)
                  </li>
                </ul>
              </div>

              <div className="bg-white rounded-2xl p-6 shadow-lg border border-gray-100 hover:shadow-xl transition-shadow">
                <div className="flex items-center gap-3 mb-4">
                  <FileText className="w-6 h-6 text-green-600" />
                  <h3 className="text-xl font-semibold text-gray-900">📦 Order & Transaction Details</h3>
                </div>
                <ul className="space-y-2 text-gray-700">
                  <li className="flex items-center gap-2">
                    <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                    Items purchased
                  </li>
                  <li className="flex items-center gap-2">
                    <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                    Order history
                  </li>
                  <li className="flex items-center gap-2">
                    <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                    Delivery instructions
                  </li>
                </ul>
              </div>
            </div>

            <div className="bg-white rounded-2xl p-6 shadow-lg border border-gray-100 hover:shadow-xl transition-shadow">
              <div className="flex items-center gap-3 mb-4">
                <Globe className="w-6 h-6 text-purple-600" />
                <h3 className="text-xl font-semibold text-gray-900">📱 Device & Usage Data</h3>
              </div>
              <div className="grid md:grid-cols-2 gap-4">
                <ul className="space-y-2 text-gray-700">
                  <li className="flex items-center gap-2">
                    <div className="w-2 h-2 bg-purple-500 rounded-full"></div>
                    IP address
                  </li>
                  <li className="flex items-center gap-2">
                    <div className="w-2 h-2 bg-purple-500 rounded-full"></div>
                    Browser type
                  </li>
                </ul>
                <ul className="space-y-2 text-gray-700">
                  <li className="flex items-center gap-2">
                    <div className="w-2 h-2 bg-purple-500 rounded-full"></div>
                    App usage behavior
                  </li>
                  <li className="flex items-center gap-2">
                    <div className="w-2 h-2 bg-purple-500 rounded-full"></div>
                    Cookies (see Section 6)
                  </li>
                </ul>
              </div>
            </div>
          </section>

          {/* Section 2: How We Use Your Information */}
          <section className="mb-16">
            <div className="flex items-center gap-4 mb-8">
              <div className="flex items-center justify-center w-12 h-12 bg-green-100 rounded-xl">
                <Eye className="w-6 h-6 text-green-600" />
              </div>
              <h2 className="text-3xl font-bold text-gray-900">2. How We Use Your Information</h2>
            </div>
            <p className="text-lg text-gray-700 mb-6 leading-relaxed">We use your data to:</p>

            <div className="bg-gradient-to-r from-green-50 to-blue-50 rounded-2xl p-8 border border-green-100">
              <div className="grid md:grid-cols-2 gap-6">
                <ul className="space-y-3">
                  <li className="flex items-start gap-3">
                    <div className="w-6 h-6 bg-green-500 rounded-full flex items-center justify-center mt-0.5">
                      <div className="w-2 h-2 bg-white rounded-full"></div>
                    </div>
                    <span className="text-gray-700">Process and fulfill orders</span>
                  </li>
                  <li className="flex items-start gap-3">
                    <div className="w-6 h-6 bg-green-500 rounded-full flex items-center justify-center mt-0.5">
                      <div className="w-2 h-2 bg-white rounded-full"></div>
                    </div>
                    <span className="text-gray-700">Deliver products to your address</span>
                  </li>
                  <li className="flex items-start gap-3">
                    <div className="w-6 h-6 bg-green-500 rounded-full flex items-center justify-center mt-0.5">
                      <div className="w-2 h-2 bg-white rounded-full"></div>
                    </div>
                    <span className="text-gray-700">Communicate order updates</span>
                  </li>
                </ul>
                <ul className="space-y-3">
                  <li className="flex items-start gap-3">
                    <div className="w-6 h-6 bg-blue-500 rounded-full flex items-center justify-center mt-0.5">
                      <div className="w-2 h-2 bg-white rounded-full"></div>
                    </div>
                    <span className="text-gray-700">Provide customer support</span>
                  </li>
                  <li className="flex items-start gap-3">
                    <div className="w-6 h-6 bg-blue-500 rounded-full flex items-center justify-center mt-0.5">
                      <div className="w-2 h-2 bg-white rounded-full"></div>
                    </div>
                    <span className="text-gray-700">Improve our website/app performance and services</span>
                  </li>
                  <li className="flex items-start gap-3">
                    <div className="w-6 h-6 bg-blue-500 rounded-full flex items-center justify-center mt-0.5">
                      <div className="w-2 h-2 bg-white rounded-full"></div>
                    </div>
                    <span className="text-gray-700">Prevent fraud and enforce legal compliance</span>
                  </li>
                </ul>
              </div>
            </div>
          </section>

          {/* Section 3: How We Share Your Information */}
          <section className="mb-16">
            <div className="flex items-center gap-4 mb-8">
              <div className="flex items-center justify-center w-12 h-12 bg-purple-100 rounded-xl">
                <Users className="w-6 h-6 text-purple-600" />
              </div>
              <h2 className="text-3xl font-bold text-gray-900">3. How We Share Your Information</h2>
            </div>
            <p className="text-lg text-gray-700 mb-6 leading-relaxed">
              We only share your information when necessary and in accordance with UAE law:
            </p>

            <div className="space-y-4">
              <div className="bg-white rounded-xl p-6 border-l-4 border-purple-500 shadow-md">
                <p className="text-gray-700 leading-relaxed">
                  <strong>With delivery personnel and logistics partners</strong> to fulfill your order
                </p>
              </div>
              <div className="bg-white rounded-xl p-6 border-l-4 border-blue-500 shadow-md">
                <p className="text-gray-700 leading-relaxed">
                  <strong>With payment service providers</strong> to process secure transactions
                </p>
              </div>
              <div className="bg-white rounded-xl p-6 border-l-4 border-red-500 shadow-md">
                <p className="text-gray-700 leading-relaxed">
                  <strong>With legal authorities</strong> if required by UAE law (e.g. fraud investigation or court
                  orders)
                </p>
              </div>
              <div className="bg-white rounded-xl p-6 border-l-4 border-green-500 shadow-md">
                <p className="text-gray-700 leading-relaxed">
                  <strong>With third-party service providers</strong> who help us operate our platform, under strict
                  data confidentiality agreements
                </p>
              </div>
            </div>

            <div className="mt-8 bg-red-50 border border-red-200 rounded-xl p-6">
              <p className="text-red-800 font-semibold text-center">We do not sell or rent your personal data.</p>
            </div>
          </section>

          {/* Section 4: Data Security */}
          <section className="mb-16">
            <div className="flex items-center gap-4 mb-8">
              <div className="flex items-center justify-center w-12 h-12 bg-red-100 rounded-xl">
                <Lock className="w-6 h-6 text-red-600" />
              </div>
              <h2 className="text-3xl font-bold text-gray-900">4. Data Security</h2>
            </div>
            <p className="text-lg text-gray-700 mb-8 leading-relaxed">
              Your data security is our top priority. We implement strict measures to:
            </p>

            <div className="grid md:grid-cols-2 gap-6">
              <div className="bg-gradient-to-br from-red-50 to-pink-50 rounded-2xl p-6 border border-red-100">
                <div className="flex items-center gap-3 mb-4">
                  <Shield className="w-6 h-6 text-red-600" />
                  <h3 className="font-semibold text-gray-900">Encryption & Security</h3>
                </div>
                <ul className="space-y-2 text-gray-700">
                  <li>• Encrypt sensitive data (e.g. payment information)</li>
                  <li>• Use secure HTTPS connections</li>
                </ul>
              </div>

              <div className="bg-gradient-to-br from-blue-50 to-indigo-50 rounded-2xl p-6 border border-blue-100">
                <div className="flex items-center gap-3 mb-4">
                  <Eye className="w-6 h-6 text-blue-600" />
                  <h3 className="font-semibold text-gray-900">Access Control</h3>
                </div>
                <ul className="space-y-2 text-gray-700">
                  <li>• Limit internal access to personal data</li>
                  <li>• Regularly audit our systems to prevent unauthorized access</li>
                </ul>
              </div>
            </div>
          </section>

          {/* Section 5: Your Rights */}
          <section className="mb-16">
            <div className="flex items-center gap-4 mb-8">
              <div className="flex items-center justify-center w-12 h-12 bg-yellow-100 rounded-xl">
                <Users className="w-6 h-6 text-yellow-600" />
              </div>
              <h2 className="text-3xl font-bold text-gray-900">5. Your Rights (as per UAE Law)</h2>
            </div>
            <p className="text-lg text-gray-700 mb-8 leading-relaxed">You have the right to:</p>

            <div className="bg-gradient-to-r from-yellow-50 via-orange-50 to-red-50 rounded-2xl p-8 border border-yellow-200">
              <div className="grid md:grid-cols-2 gap-6">
                <div className="space-y-4">
                  <div className="flex items-center gap-3">
                    <div className="w-8 h-8 bg-yellow-500 rounded-full flex items-center justify-center">
                      <Eye className="w-4 h-4 text-white" />
                    </div>
                    <span className="text-gray-700 font-medium">Access your personal data</span>
                  </div>
                  <div className="flex items-center gap-3">
                    <div className="w-8 h-8 bg-orange-500 rounded-full flex items-center justify-center">
                      <FileText className="w-4 h-4 text-white" />
                    </div>
                    <span className="text-gray-700 font-medium">Request correction or deletion of your data</span>
                  </div>
                </div>
                <div className="space-y-4">
                  <div className="flex items-center gap-3">
                    <div className="w-8 h-8 bg-red-500 rounded-full flex items-center justify-center">
                      <Shield className="w-4 h-4 text-white" />
                    </div>
                    <span className="text-gray-700 font-medium">Withdraw consent (where applicable)</span>
                  </div>
                  <div className="flex items-center gap-3">
                    <div className="w-8 h-8 bg-purple-500 rounded-full flex items-center justify-center">
                      <FileText className="w-4 h-4 text-white" />
                    </div>
                    <span className="text-gray-700 font-medium">File a complaint with the UAE Data Office</span>
                  </div>
                </div>
              </div>

              <div className="mt-8 bg-white rounded-xl p-6 border border-gray-200">
                <p className="text-gray-700 mb-4">To exercise your rights, please contact us at:</p>
                <div className="flex items-center gap-2 text-blue-600 font-semibold">
                  <Mail className="w-5 h-5" />
                  📧 <EMAIL>
                </div>
              </div>
            </div>
          </section>

          {/* Section 6: Cookies and Tracking Technologies */}
          <section className="mb-16">
            <div className="flex items-center gap-4 mb-8">
              <div className="flex items-center justify-center w-12 h-12 bg-indigo-100 rounded-xl">
                <Globe className="w-6 h-6 text-indigo-600" />
              </div>
              <h2 className="text-3xl font-bold text-gray-900">6. Cookies and Tracking Technologies</h2>
            </div>
            <p className="text-lg text-gray-700 mb-6 leading-relaxed">We use cookies to:</p>

            <div className="grid md:grid-cols-3 gap-4 mb-8">
              <div className="bg-white rounded-xl p-6 shadow-md border border-gray-100 text-center">
                <div className="w-12 h-12 bg-indigo-100 rounded-full flex items-center justify-center mx-auto mb-4">
                  <FileText className="w-6 h-6 text-indigo-600" />
                </div>
                <p className="text-gray-700">Store your session and cart details</p>
              </div>
              <div className="bg-white rounded-xl p-6 shadow-md border border-gray-100 text-center">
                <div className="w-12 h-12 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                  <Eye className="w-6 h-6 text-green-600" />
                </div>
                <p className="text-gray-700">Analyze traffic and user behavior</p>
              </div>
              <div className="bg-white rounded-xl p-6 shadow-md border border-gray-100 text-center">
                <div className="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
                  <Users className="w-6 h-6 text-blue-600" />
                </div>
                <p className="text-gray-700">Improve user experience</p>
              </div>
            </div>

            <div className="bg-amber-50 border border-amber-200 rounded-xl p-6">
              <p className="text-amber-800 leading-relaxed">
                You can control cookie settings in your browser. However, disabling cookies may affect your experience
                on our platform.
              </p>
            </div>
          </section>

          {/* Section 7: Data Retention */}
          <section className="mb-16">
            <div className="flex items-center gap-4 mb-8">
              <div className="flex items-center justify-center w-12 h-12 bg-teal-100 rounded-xl">
                <FileText className="w-6 h-6 text-teal-600" />
              </div>
              <h2 className="text-3xl font-bold text-gray-900">7. Data Retention</h2>
            </div>
            <p className="text-lg text-gray-700 mb-6 leading-relaxed">
              We retain your data only as long as necessary for:
            </p>

            <div className="space-y-4">
              <div className="bg-white rounded-xl p-6 border-l-4 border-teal-500 shadow-md">
                <p className="text-gray-700">Fulfilling orders and customer service</p>
              </div>
              <div className="bg-white rounded-xl p-6 border-l-4 border-blue-500 shadow-md">
                <p className="text-gray-700">Legal and regulatory compliance</p>
              </div>
              <div className="bg-white rounded-xl p-6 border-l-4 border-purple-500 shadow-md">
                <p className="text-gray-700">Internal analytics (with anonymized data if possible)</p>
              </div>
            </div>
          </section>

          {/* Section 8: International Data Transfers */}
          <section className="mb-16">
            <div className="flex items-center gap-4 mb-8">
              <div className="flex items-center justify-center w-12 h-12 bg-cyan-100 rounded-xl">
                <Globe className="w-6 h-6 text-cyan-600" />
              </div>
              <h2 className="text-3xl font-bold text-gray-900">8. International Data Transfers</h2>
            </div>
            <div className="bg-cyan-50 border border-cyan-200 rounded-xl p-6">
              <p className="text-cyan-800 leading-relaxed">
                While our services are UAE-based, if any data is processed outside the UAE, we ensure appropriate data
                protection safeguards in accordance with UAE regulations.
              </p>
            </div>
          </section>

          {/* Section 9: Children's Privacy */}
          <section className="mb-16">
            <div className="flex items-center gap-4 mb-8">
              <div className="flex items-center justify-center w-12 h-12 bg-pink-100 rounded-xl">
                <Shield className="w-6 h-6 text-pink-600" />
              </div>
              <h2 className="text-3xl font-bold text-gray-900">9. Children's Privacy</h2>
            </div>
            <div className="bg-pink-50 border border-pink-200 rounded-xl p-6">
              <p className="text-pink-800 leading-relaxed">
                EEF EXPRESS is not intended for use by children under the age of 18. We do not knowingly collect
                personal information from minors.
              </p>
            </div>
          </section>

          {/* Section 10: Policy Updates */}
          <section className="mb-16">
            <div className="flex items-center gap-4 mb-8">
              <div className="flex items-center justify-center w-12 h-12 bg-orange-100 rounded-xl">
                <FileText className="w-6 h-6 text-orange-600" />
              </div>
              <h2 className="text-3xl font-bold text-gray-900">10. Policy Updates</h2>
            </div>
            <div className="bg-orange-50 border border-orange-200 rounded-xl p-6">
              <p className="text-orange-800 leading-relaxed">
                We may update this policy from time to time. Changes will be posted on this page with the "Last Updated"
                date. Continued use of our platform implies acceptance of the updated policy.
              </p>
            </div>
          </section>

          {/* Section 11: Contact Us */}
          <section className="mb-16">
            <div className="flex items-center gap-4 mb-8">
              <div className="flex items-center justify-center w-12 h-12 bg-emerald-100 rounded-xl">
                <Phone className="w-6 h-6 text-emerald-600" />
              </div>
              <h2 className="text-3xl font-bold text-gray-900">11. Contact Us</h2>
            </div>
            <p className="text-lg text-gray-700 mb-8 leading-relaxed">
              For any privacy-related questions or requests, reach out to us:
            </p>

            <div className="bg-gradient-to-r from-emerald-50 to-teal-50 rounded-2xl p-8 border border-emerald-200">
              <div className="grid md:grid-cols-2 gap-8">
                <div className="space-y-6">
                  <div className="flex items-center gap-4">
                    <div className="w-12 h-12 bg-emerald-500 rounded-full flex items-center justify-center">
                      <Mail className="w-6 h-6 text-white" />
                    </div>
                    <div>
                      <p className="text-sm text-gray-600">Email Support</p>
                      <p className="text-lg font-semibold text-emerald-700">📧 <EMAIL></p>
                    </div>
                  </div>

                  <div className="flex items-center gap-4">
                    <div className="w-12 h-12 bg-teal-500 rounded-full flex items-center justify-center">
                      <MapPin className="w-6 h-6 text-white" />
                    </div>
                    <div>
                      <p className="text-sm text-gray-600">Business Address</p>
                      <p className="text-lg font-semibold text-teal-700">
                        📍 EEF EXPRESS, [Insert Business Address], UAE
                      </p>
                    </div>
                  </div>
                </div>

                <div className="bg-white rounded-xl p-6 shadow-md">
                  <h3 className="text-xl font-semibold text-gray-900 mb-4">Quick Contact</h3>
                  <p className="text-gray-700 mb-4">
                    We're here to help with any privacy concerns or questions you may have about your data.
                  </p>
                  <div className="bg-emerald-100 rounded-lg p-4">
                    <p className="text-emerald-800 font-medium text-center">Response time: Within 24-48 hours</p>
                  </div>
                </div>
              </div>
            </div>
          </section>

          {/* Final Agreement Section */}
          <section className="mb-16">
            <div className="bg-gradient-to-r from-blue-600 via-purple-600 to-indigo-700 text-white rounded-2xl p-8 text-center">
              <div className="max-w-3xl mx-auto">
                <div className="inline-flex items-center justify-center w-16 h-16 bg-white/20 rounded-full mb-6 backdrop-blur-sm">
                  <Shield className="w-8 h-8" />
                </div>
                <h3 className="text-2xl font-bold mb-4">Agreement to Privacy Policy</h3>
                <p className="text-lg text-blue-100 leading-relaxed">
                  By using EEF EXPRESS, you agree to the terms outlined in this Privacy Policy.
                </p>
              </div>
            </div>
          </section>
        </div>
      </div>
    </div>
  );
}
