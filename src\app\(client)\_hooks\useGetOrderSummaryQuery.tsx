import { useQuery } from "@tanstack/react-query";

import { getOrderSummary, GetOrderSummaryParams } from "@/services/checkout/getOrderSummary.service";

export const useGetOrderSummaryQuery = (params: GetOrderSummaryParams = {}) => {
  return useQuery({
    queryKey: ["order-summary", params],
    queryFn: () => getOrderSummary(params),
    enabled: true,
  });
};

export default useGetOrderSummaryQuery;
