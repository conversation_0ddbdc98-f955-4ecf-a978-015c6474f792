"use client";

import React, { useState } from "react";
import { <PERSON>Check } from "lucide-react";

import LoadingSpinner from "@/components/LoadingSpinner";
import { Button } from "@/components/ui/button";
import { Checkbox } from "@/components/ui/checkbox";

interface ConfirmationProps {
  submitText: string;
  totalPrice?: number;
  currentStep: number;
  totalSteps: number;
  isSubmitting?: boolean;
}

const Confirmation = ({ totalPrice, submitText, currentStep, totalSteps, isSubmitting }: ConfirmationProps) => {
  const [agreedToTerms, setAgreedToTerms] = useState(false);

  return (
    <div className="w-full border rounded-lg p-5 tracking-tight space-y-6">
      {/* header */}
      <div className="mb-6 flex justify-between items-end">
        <div className="space-y-1">
          <h3 className="font-bold text-xl">Confirmation</h3>
          <p className="text-sm text-gray-400 tracking-normal">
            We are getting to the end. Just few clicks and your order is ready!
          </p>
        </div>

        <p className="text-gray-400 text-sm">
          Step {currentStep} of {totalSteps}
        </p>
      </div>
      {/* header ends */}

      {/* Terms checkbox */}
      <div className="flex items-center space-x-2">
        <Checkbox
          id="terms"
          checked={agreedToTerms}
          onCheckedChange={(checked) => setAgreedToTerms(checked as boolean)}
          className="h-5 w-5 rounded border-gray-300"
        />
        <label
          htmlFor="terms"
          className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
        >
          I agree with our terms and conditions and privacy policy.
        </label>
      </div>

      {/* Total Price */}
      {totalPrice && (
        <div className="mt-6 p-5 border-y bg-blue-50 -mx-5">
          <div className="flex justify-between items-center">
            <div>
              <h6 className="font-semibold text-gray-900">Total Price</h6>
              <p className="text-sm text-gray-500">Overall price which includes delivery price</p>
            </div>
            <p className="text-xl font-bold text-gray-900">549 AED</p>
          </div>
        </div>
      )}

      {/* Submit button */}
      <div>
        <Button type="submit" size="lg" disabled={!agreedToTerms || isSubmitting}>
          {isSubmitting ? <LoadingSpinner size="sm" /> : submitText}
        </Button>
      </div>

      {/* Security message */}
      <div className="flex items-start space-x-3 pt-4">
        <ShieldCheck className="h-6 w-6 text-black mt-0.5" />
        <div>
          <p className="font-medium text-sm">All your data are safe</p>
          <p className="text-gray-400 text-sm">
            We are using the most advanced security to provide you the best experience ever.
          </p>
        </div>
      </div>
    </div>
  );
};

export default Confirmation;
