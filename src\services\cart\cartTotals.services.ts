import { apiRequest } from "@/utils/axios-utils";

export interface CartTotalsData {
  subtotal: number;
  itemCount: number;
  totalItems: number;
}

export interface CartTotalsResponse {
  data: CartTotalsData;
  meta: null;
}

export const getCartTotals = async (): Promise<CartTotalsData> => {
  const response = await apiRequest<CartTotalsResponse>({
    url: "/cart/totals",
    method: "GET",
    showToast: false,
  });
  return response.data;
};
