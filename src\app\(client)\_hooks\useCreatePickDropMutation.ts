import { useMutation } from "@tanstack/react-query";
import { toast } from "sonner";

import { createPickDrop } from "@/services/pickDrop/createPickDrop.service";
import { PickDropResponse } from "@/types/pickDrop";

export const useCreatePickDropMutation = () => {
  return useMutation<PickDropResponse, Error, FormData>({
    mutationFn: (formData: FormData) => createPickDrop(formData),
    onSuccess: () => {
      toast.success("Pick & Drop order created successfully!");
    },
    onError: () => {
      toast.error(`Failed to create Pick & Drop order`);
    },
  });
};
