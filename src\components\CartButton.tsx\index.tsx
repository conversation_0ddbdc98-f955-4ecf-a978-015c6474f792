import React from "react";
import { ShoppingCart } from "lucide-react";

import { useCartTotalQuery } from "@/hooks/cart/useCartTotalQuery";
import { Badge } from "../ui/badge";

function CartButton() {
  const { data: cartTotals, isLoading, isError } = useCartTotalQuery();

  return (
    <span className="flex items-center gap-2 ">
      <span className="relative">
        <ShoppingCart className="text-gray-700" />
        {!isLoading && !isError && cartTotals && (
          <Badge variant={"destructive"} className="absolute -top-5.5 -right-4 text-xs rounded-full">
            {cartTotals?.totalItems}
          </Badge>
        )}
      </span>
    </span>
  );
}

export default CartButton;
