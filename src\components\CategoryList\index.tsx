"use client";

import React from "react";

import { useGetCategoriesWithCountQuery } from "@/app/(client)/_hooks/useGetCategoriesWithCountQuery";
import { Skeleton } from "@/components/ui/skeleton";

interface CategoryListProps {
  onCategoryClick?: (categoryId: number) => void;
  selectedCategoryId?: number;
}

function CategoryList({ onCategoryClick, selectedCategoryId }: CategoryListProps) {
  const { data: categories, isLoading, isError } = useGetCategoriesWithCountQuery();

  const handleCategoryClick = (categoryId: number) => {
    if (onCategoryClick) {
      onCategoryClick(categoryId);
    }
  };

  if (isLoading) {
    return (
      <ul className="space-y-3">
        {Array(5)
          .fill(0)
          .map((_, index) => (
            <li key={index} className="flex justify-between">
              <Skeleton className="h-4 w-24" />
              <Skeleton className="h-4 w-8" />
            </li>
          ))}
      </ul>
    );
  }

  if (isError) {
    return <div className="text-sm text-red-500">Failed to load categories. Please try again later.</div>;
  }

  return (
    <ul className="space-y-3">
      {categories?.map((category) => (
        <li
          key={category.id}
          className={`flex justify-between cursor-pointer transition-colors ${
            selectedCategoryId === category.id ? "text-black font-medium" : "text-gray-500 hover:text-black"
          }`}
          onClick={() => handleCategoryClick(category.id)}
        >
          <div className="w-full flex justify-between">
            <span className="tracking-tight">{category.name}</span>
            <span className="text-xs">{category.productCount}</span>
          </div>
        </li>
      ))}
    </ul>
  );
}

export default CategoryList;
