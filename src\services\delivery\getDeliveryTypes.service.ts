import { DeliveryType } from "@/types/deliveryType";
import { apiRequest } from "@/utils/axios-utils";

export interface GetDeliveryTypesResponse {
  data: DeliveryType[];
  meta: null;
}

export const getDeliveryTypes = async (): Promise<DeliveryType[]> => {
  const response = await apiRequest<GetDeliveryTypesResponse>({
    url: "/delivery-types",
    method: "GET",
    showToast: false,
  });
  return response.data;
};
