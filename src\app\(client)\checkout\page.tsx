"use client";

import React, { Suspense, useState } from "react";

import { Form } from "@/components/ui/form";
import { Skeleton } from "@/components/ui/skeleton";
import { CheckoutFormValues } from "@/lib/schemas/checkoutFormSchema";
import DeliveryOption from "../_components/DeliveryOption";
import useGetOrderSummaryQuery from "../_hooks/useGetOrderSummaryQuery";
import { useCheckoutForm } from "./_hooks/useCheckoutForm";
import { useCreateOrderMutation } from "./_hooks/useCreateOrderMutation";
import Confirmation from "./components/Confirmation";
import DeliveryInfo from "./components/DeliveryInfo";
import OrderSummary from "./components/OrderSummary";
import PaymentMethod from "./components/PaymentMethod";

// Wrapper component that uses hooks requiring Suspense
function CheckoutContent() {
  const [deliveryType, setDeliveryType] = useState<string | undefined>(undefined);
  const [scheduledDateTime, setScheduledDateTime] = useState<Date | null>(null);

  const {
    data: orderSummary,
    isLoading: isLoadingOrderSummary,
    error: orderSummaryError,
  } = useGetOrderSummaryQuery({
    deliveryType,
    scheduledDateTime: scheduledDateTime ? scheduledDateTime.toISOString() : undefined,
  });

  const createOrderMutation = useCreateOrderMutation();

  const handleSubmitOrder = async (data: CheckoutFormValues) => {
    // Submit the order using the mutation
    createOrderMutation.mutate(data);
  };

  const { form, isSubmitting, handleSubmit } = useCheckoutForm({
    onSubmit: handleSubmitOrder,
  });

  const handleDeliveryOptionSelect = (deliveryTypeId: number, dateTime?: Date | null) => {
    const selectedType = orderSummary?.deliveryOptions.find((opt) => opt.id === deliveryTypeId);
    if (selectedType) {
      // Prevent unnecessary re-renders by checking if values are different
      if (selectedType.type !== deliveryType || dateTime?.getTime() !== scheduledDateTime?.getTime()) {
        setDeliveryType(selectedType.type);
        setScheduledDateTime(dateTime as Date);

        // Update form values
        form.setValue("deliveryType", selectedType.type);
        form.setValue("scheduledDateTime", dateTime as Date);
      }
    }
  };

  return (
    <div className="max-width-wrapper mt-8 mb-20">
      <Form {...form}>
        <form onSubmit={handleSubmit} className="grid md:grid-cols-12 gap-8">
          <div className="md:col-span-7 space-y-8">
            <DeliveryInfo control={form.control} />
            <DeliveryOption currentStep={2} totalSteps={4} onSelect={handleDeliveryOptionSelect} />
            <PaymentMethod control={form.control} />
            <Confirmation
              currentStep={4}
              totalSteps={4}
              isSubmitting={isSubmitting || createOrderMutation.isPending}
              onSubmit={handleSubmit}
              submitText="Proceed to payment"
            />
          </div>
          <div className="md:col-span-5">
            <OrderSummary data={orderSummary} isLoading={isLoadingOrderSummary} error={orderSummaryError as Error} />
          </div>
        </form>
      </Form>
    </div>
  );
}

// Loading fallback for Suspense
function CheckoutLoading() {
  return (
    <div className="max-width-wrapper mt-8 mb-20">
      <div className="grid md:grid-cols-12 gap-8">
        <div className="md:col-span-7 space-y-8">
          <Skeleton className="h-64 w-full rounded-lg" />
          <Skeleton className="h-40 w-full rounded-lg" />
          <Skeleton className="h-40 w-full rounded-lg" />
          <Skeleton className="h-24 w-full rounded-lg" />
        </div>
        <div className="md:col-span-5">
          <div className="w-full border rounded-lg p-5 space-y-5">
            <div className="space-y-3 mb-5">
              <Skeleton className="h-7 w-40" />
              <Skeleton className="h-4 w-60" />
            </div>
            <div className="space-y-4">
              {[1, 2, 3].map((i) => (
                <div key={i} className="flex items-center space-x-4 border-b last:border-b-0 p-5">
                  <Skeleton className="w-16 h-16 rounded-md" />
                  <div className="flex-1 space-y-2">
                    <Skeleton className="h-5 w-40" />
                    <Skeleton className="h-4 w-24" />
                  </div>
                </div>
              ))}
            </div>
            <div className="flex justify-between items-center -mb-5 -mx-5 p-5">
              <Skeleton className="h-4 w-20" />
              <Skeleton className="h-4 w-16" />
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

// Main component with Suspense boundary
export default function Checkout() {
  return (
    <Suspense fallback={<CheckoutLoading />}>
      <CheckoutContent />
    </Suspense>
  );
}
