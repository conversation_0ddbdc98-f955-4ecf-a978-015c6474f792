import { Eye, FileText, Mail, Shield, Users } from "lucide-react";

export default function YourRightsSection() {
  return (
    <section className="mb-16">
      <div className="flex items-center gap-4 mb-8">
        <div className="flex items-center justify-center w-12 h-12 bg-yellow-100 rounded-xl">
          <Users className="w-6 h-6 text-yellow-600" />
        </div>
        <h2 className="section-title text-gray-900">5. Your Rights (as per UAE Law)</h2>
      </div>
      <p className="text-gray-700 mb-8 leading-relaxed">You have the right to:</p>

      <div className="bg-gradient-to-r from-yellow-50 via-orange-50 to-red-50 rounded-2xl p-8 border border-yellow-200">
        <div className="grid md:grid-cols-2 gap-6">
          <div className="space-y-4">
            <div className="flex items-center gap-3">
              <div className="w-8 h-8 bg-yellow-500 rounded-full flex items-center justify-center">
                <Eye className="w-4 h-4 text-white" />
              </div>
              <span className="text-gray-700 font-medium">Access your personal data</span>
            </div>
            <div className="flex items-center gap-3">
              <div className="w-8 h-8 bg-orange-500 rounded-full flex items-center justify-center">
                <FileText className="w-4 h-4 text-white" />
              </div>
              <span className="text-gray-700 font-medium">Request correction or deletion of your data</span>
            </div>
          </div>
          <div className="space-y-4">
            <div className="flex items-center gap-3">
              <div className="w-8 h-8 bg-red-500 rounded-full flex items-center justify-center">
                <Shield className="w-4 h-4 text-white" />
              </div>
              <span className="text-gray-700 font-medium">Withdraw consent (where applicable)</span>
            </div>
            <div className="flex items-center gap-3">
              <div className="w-8 h-8 bg-purple-500 rounded-full flex items-center justify-center">
                <FileText className="w-4 h-4 text-white" />
              </div>
              <span className="text-gray-700 font-medium">File a complaint with the UAE Data Office</span>
            </div>
          </div>
        </div>

        <div className="mt-8 bg-white rounded-xl p-6 border border-gray-200">
          <p className="text-gray-700 mb-4">To exercise your rights, please contact us at:</p>
          <div className="flex items-center gap-2 text-blue-600 font-semibold">
            <Mail className="w-5 h-5" />
            <EMAIL>
          </div>
        </div>
      </div>
    </section>
  );
}
