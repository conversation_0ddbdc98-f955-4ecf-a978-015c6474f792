import { useQuery } from "@tanstack/react-query";

import { getOrderDetail } from "@/services/orders/getOrderDetail.service";

export const useGetOrderDetailQuery = (orderId: string | null) => {
  return useQuery({
    queryKey: ["order-detail", orderId],
    queryFn: () => getOrderDetail(orderId as string),
    enabled: !!orderId, // Only run the query if orderId exists
  });
};

export default useGetOrderDetailQuery;
