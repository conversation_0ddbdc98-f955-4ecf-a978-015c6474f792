"use client";

import { useMutation } from "@tanstack/react-query";
import { toast } from "sonner";

import { LoginCredentials, LoginResponse, loginUser } from "@/services/auth/login.service";
import { saveToken } from "@/utils/save-token";

const getReturnUrl = (): string => {
  const urlParams = new URLSearchParams(window.location.search);
  return urlParams.get("returnUrl") || "/";
};

export const useLoginMutation = () => {
  return useMutation({
    mutationFn: (data: LoginCredentials) => loginUser(data),
    onSuccess: (response: LoginResponse) => {
      toast.success("Logged in successfully.");
      saveToken(response.jwt);

      const returnUrl = getReturnUrl();
      window.location.href = returnUrl;
    },
  });
};
