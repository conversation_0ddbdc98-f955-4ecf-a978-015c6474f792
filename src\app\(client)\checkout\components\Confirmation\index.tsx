import React from "react";
import { <PERSON><PERSON>he<PERSON> } from "lucide-react";

import LoadingSpinner from "@/components/LoadingSpinner";
import { Button } from "@/components/ui/button";

interface ConfirmationProps {
  currentStep: number;
  totalSteps: number;
  isSubmitting: boolean;
  onSubmit: () => void;
  submitText: string;
}

function Confirmation({ currentStep, totalSteps, isSubmitting, onSubmit, submitText }: ConfirmationProps) {
  return (
    <div className="w-full border rounded-lg p-5 tracking-tight space-y-5">
      {/* header */}
      <div className="mb-8 flex justify-between items-end">
        <div className="space-y-1">
          <h3 className="font-bold text-xl">Confirmation</h3>
          <p className="text-sm text-gray-400 tracking-normal">Review your order and proceed to payment</p>
        </div>

        <p className="text-gray-400 text-sm">
          Step {currentStep} of {totalSteps}
        </p>
      </div>
      {/* header ends */}

      {/* body */}
      <div className="space-y-4">
        <p className="text-sm text-gray-600">
          By clicking the button below, you confirm that you have reviewed your order details and agree to the terms and
          conditions of our service.
        </p>

        <Button type="submit" className="w-fit min-w-36" size="lg" onClick={onSubmit} disabled={isSubmitting}>
          {isSubmitting ? <LoadingSpinner size="sm" /> : submitText}
        </Button>

        {/* Security message */}
        <div className="flex items-start space-x-3 pt-4">
          <ShieldCheck className="h-6 w-6 text-black mt-0.5" />
          <div>
            <p className="font-medium text-sm">All your data are safe</p>
            <p className="text-gray-400 text-sm">
              We are using the most advanced security to provide you the best experience ever.
            </p>
          </div>
        </div>
      </div>
    </div>
  );
}

export default Confirmation;
