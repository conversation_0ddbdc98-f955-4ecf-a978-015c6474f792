import { FileText } from "lucide-react";

export default function InformationWeCollectSection() {
  return (
    <section className="mb-16">
      <div className="flex items-center gap-4 mb-8">
        <div className="flex items-center justify-center w-12 h-12 bg-blue-100 rounded-xl">
          <FileText className="w-6 h-6 text-blue-600" />
        </div>
        <h2 className="section-title text-gray-900">1. Information We Collect</h2>
      </div>
      <p className="text-gray-700 mb-8 leading-relaxed">
        When you register or use EEF EXPRESS, we may collect the following types of information:
      </p>

      <div className="grid md:grid-cols-2 gap-6 mb-8">
        <div className="bg-white rounded-2xl p-6 shadow-lg border border-gray-100 hover:shadow-xl transition-shadow">
          <div className="flex items-center gap-3 mb-4">
            {/* <Users className="w-6 h-6 text-blue-600" /> */}
            <h3 className="text-xl font-semibold text-gray-900">🧾 Personal Information</h3>
          </div>
          <ul className="space-y-2 text-gray-700">
            <li className="flex items-center gap-2">
              <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
              Full Name
            </li>
            <li className="flex items-center gap-2">
              <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
              Contact Number
            </li>
            <li className="flex items-center gap-2">
              <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
              Email Address
            </li>
            <li className="flex items-center gap-2">
              <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
              Delivery Address
            </li>
            <li className="flex items-center gap-2">
              <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
              Payment Information (via secure third-party gateways)
            </li>
          </ul>
        </div>

        <div className="bg-white rounded-2xl p-6 shadow-lg border border-gray-100 hover:shadow-xl transition-shadow">
          <div className="flex items-center gap-3 mb-4">
            {/* <FileText className="w-6 h-6 text-green-600" /> */}
            <h3 className="text-xl font-semibold text-gray-900">📦 Order & Transaction Details</h3>
          </div>
          <ul className="space-y-2 text-gray-700">
            <li className="flex items-center gap-2">
              <div className="w-2 h-2 bg-green-500 rounded-full"></div>
              Items purchased
            </li>
            <li className="flex items-center gap-2">
              <div className="w-2 h-2 bg-green-500 rounded-full"></div>
              Order history
            </li>
            <li className="flex items-center gap-2">
              <div className="w-2 h-2 bg-green-500 rounded-full"></div>
              Delivery instructions
            </li>
          </ul>
        </div>
      </div>

      <div className="bg-white rounded-2xl p-6 shadow-lg border border-gray-100 hover:shadow-xl transition-shadow">
        <div className="flex items-center gap-3 mb-4">
          {/* <Globe className="w-6 h-6 text-purpxle-600" /> */}
          <h3 className="text-xl font-semibold text-gray-900">📱 Device & Usage Data</h3>
        </div>
        <div className="grid md:grid-cols-2 gap-4">
          <ul className="space-y-2 text-gray-700">
            <li className="flex items-center gap-2">
              <div className="w-2 h-2 bg-purple-500 rounded-full"></div>
              IP address
            </li>
            <li className="flex items-center gap-2">
              <div className="w-2 h-2 bg-purple-500 rounded-full"></div>
              Browser type
            </li>
          </ul>
          <ul className="space-y-2 text-gray-700">
            <li className="flex items-center gap-2">
              <div className="w-2 h-2 bg-purple-500 rounded-full"></div>
              App usage behavior
            </li>
            <li className="flex items-center gap-2">
              <div className="w-2 h-2 bg-purple-500 rounded-full"></div>
              Cookies (see Section 6)
            </li>
          </ul>
        </div>
      </div>
    </section>
  );
}
