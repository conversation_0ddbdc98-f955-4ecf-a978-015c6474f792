"use client";

import React from "react";
import Image from "next/image";
import { Clock, CreditCard, Truck } from "lucide-react";

import { Badge } from "@/components/ui/badge";
import { cn } from "@/lib/utils";
import { formatDate } from "@/utils/formatDate";

export interface OrderItem {
  id: string;
  name: string;
  quantity: number;
  price: number;
  imageSrc: string;
}

export interface OrderType {
  id: number;
  orderNumber: string;
  orderStatus: string;
  paymentStatus: string;
  createdAt: string;
  items: OrderItem[];
  deliveryType: string;
  deliveryFee: number;
}

// Payment status badge colors
const paymentStatusColors = {
  pending: "bg-yellow-100 text-yellow-800 border-yellow-200",
  processing: "bg-blue-100 text-blue-800 border-blue-200",
  completed: "bg-green-100 text-green-800 border-green-200",
  failed: "bg-red-100 text-red-800 border-red-200",
  refund: "bg-purple-100 text-purple-800 border-purple-200",
};

// Shipping status badge colors
const shippingStatusColors = {
  Pending: "bg-yellow-100 text-yellow-800 border-yellow-200",
  Processing: "bg-blue-100 text-blue-800 border-blue-200",
  Packaging: "bg-indigo-100 text-indigo-800 border-indigo-200",
  Shipped: "bg-cyan-100 text-cyan-800 border-cyan-200",
  Delivered: "bg-green-100 text-green-800 border-green-200",
  Cancelled: "bg-red-100 text-red-800 border-red-200",
};

const Order: React.FC<OrderType> = ({
  deliveryFee,
  deliveryType,
  orderNumber,
  orderStatus,
  paymentStatus,
  createdAt,
  items,
}) => {
  const totalPrice = items.reduce((sum, item) => sum + item.price * item.quantity, 0);
  const totalItems = items.reduce((sum, item) => sum + item.quantity, 0);
  const formattedDate = formatDate(new Date(createdAt));

  // Get appropriate status colors
  const paymentStatusColor =
    paymentStatusColors[paymentStatus as keyof typeof paymentStatusColors] ||
    "bg-gray-100 text-gray-800 border-gray-200";

  const shippingStatusColor =
    shippingStatusColors[orderStatus as keyof typeof shippingStatusColors] ||
    "bg-gray-100 text-gray-800 border-gray-200";

  return (
    <div className="bg-white rounded-lg border overflow-hidden">
      {/* Order Header */}
      <div className="bg-gray-50 p-4 border-b flex flex-col sm:flex-row justify-between gap-3">
        <div className="space-y-1">
          <div className="flex items-center gap-2">
            <span className="text-sm text-gray-500">Order #:</span>
            <span className="font-medium text-green-800">{orderNumber}</span>
          </div>
          <div className="text-sm text-gray-500">Placed on {formattedDate}</div>
        </div>

        <div className="flex flex-col sm:items-end gap-1">
          <div className="flex items-center gap-2">
            <CreditCard size={14} className="text-gray-500" />
            <span className="text-sm text-gray-500">Payment:</span>
            <Badge variant="outline" className={cn("text-xs font-medium", paymentStatusColor)}>
              {paymentStatus.charAt(0).toUpperCase() + paymentStatus.slice(1)}
            </Badge>
          </div>

          <div className="flex items-center gap-2">
            <Truck size={14} className="text-gray-500" />
            <span className="text-sm text-gray-500">Status:</span>
            <Badge variant="outline" className={cn("text-xs font-medium", shippingStatusColor)}>
              {orderStatus}
            </Badge>
          </div>
        </div>
      </div>

      {/* Order Items */}
      <div className="p-4 divide-y">
        {items.map((item, index) => (
          <div key={item.id} className={cn("py-4", index === 0 ? "pt-0" : "")}>
            <div className="flex items-start gap-4">
              <div className="relative w-20 h-20 rounded-md overflow-hidden bg-gray-50 flex-shrink-0">
                <Image
                  src={item.imageSrc || "/assets/images/placeholder.png"}
                  alt={item.name}
                  fill
                  className="object-cover"
                />
              </div>

              <div className="flex-1 min-w-0">
                <h4 className="font-medium text-gray-900 truncate">{item.name}</h4>
                <div className="mt-1 flex flex-wrap gap-x-4 gap-y-1 text-sm text-gray-500">
                  <span>Qty: {item.quantity}</span>
                  <span>Price: {item.price.toFixed(2)} AED</span>
                  <span>Total: {(item.price * item.quantity).toFixed(2)} AED</span>
                </div>

                {/* Item status tracking - simplified version */}
                {orderStatus !== "Cancelled" && (
                  <div className="mt-2 flex items-center text-xs text-gray-500">
                    <Clock size={12} className="mr-1" />
                    {orderStatus === "Delivered" ? "Delivered" : "Estimated delivery"}:
                    <span className="font-medium ml-1">
                      {orderStatus === "Delivered" ? "Completed" : "Based on package"}
                    </span>
                  </div>
                )}
              </div>
            </div>
          </div>
        ))}
      </div>

      {/* Order Footer */}
      <div className="bg-gray-50 p-4 border-t flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div>
          <div className="text-sm font-medium">
            Total: <span className="text-green-700">{totalPrice.toFixed(2)} AED</span>
          </div>
          <div className="text-xs text-gray-500">
            {totalItems} {totalItems === 1 ? "item" : "items"}
          </div>
        </div>

        <div>
          <div className="text-sm font-medium">
            Delivery: <span className="text-blue-900">{deliveryType}</span>
          </div>
          <div className="text-xs text-gray-500">Delivery fee: {deliveryFee} AED</div>
        </div>
      </div>
    </div>
  );
};

export default Order;
