import { Users } from "lucide-react";

const sharingItems = [
  {
    title: "With delivery personnel and logistics partners",
    description: "to fulfill your order",
    borderColor: "border-purple-500",
  },
  {
    title: "With payment service providers",
    description: "to process secure transactions",
    borderColor: "border-blue-500",
  },
  {
    title: "With legal authorities",
    description: "if required by UAE law (e.g. fraud investigation or court orders)",
    borderColor: "border-red-500",
  },
  {
    title: "With third-party service providers",
    description: "who help us operate our platform, under strict data confidentiality agreements",
    borderColor: "border-green-500",
  },
];

export default function HowWeShareInfoSection() {
  return (
    <section className="mb-16">
      <div className="flex items-center gap-4 mb-8">
        <div className="flex items-center justify-center w-12 h-12 bg-purple-100 rounded-xl">
          <Users className="w-6 h-6 text-purple-600" />
        </div>
        <h2 className="section-title text-gray-900">3. How We Share Your Information</h2>
      </div>
      <p className="text-gray-700 mb-6 leading-relaxed">
        We only share your information when necessary and in accordance with UAE law:
      </p>

      <div className="space-y-4">
        {sharingItems.map((item, index) => (
          <div key={index} className={`bg-white rounded-xl p-6 border-l-4 ${item.borderColor} shadow-md`}>
            <p className="text-gray-700 leading-relaxed">
              <strong>{item.title}</strong> {item.description}
            </p>
          </div>
        ))}
      </div>

      <div className="mt-8 bg-red-50 border border-red-200 rounded-xl p-6">
        <p className="text-red-800 font-semibold text-center">We do not sell or rent your personal data.</p>
      </div>
    </section>
  );
}
