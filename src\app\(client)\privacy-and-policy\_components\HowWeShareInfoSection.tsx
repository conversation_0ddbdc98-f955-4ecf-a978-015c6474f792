import { Users } from "lucide-react";

export default function HowWeShareInfoSection() {
  return (
    <section className="mb-16">
      <div className="flex items-center gap-4 mb-8">
        <div className="flex items-center justify-center w-12 h-12 bg-purple-100 rounded-xl">
          <Users className="w-6 h-6 text-purple-600" />
        </div>
        <h2 className="text-3xl font-bold text-gray-900">3. How We Share Your Information</h2>
      </div>
      <p className="text-lg text-gray-700 mb-6 leading-relaxed">
        We only share your information when necessary and in accordance with UAE law:
      </p>
      
      <div className="space-y-4">
        <div className="bg-white rounded-xl p-6 border-l-4 border-purple-500 shadow-md">
          <p className="text-gray-700 leading-relaxed">
            <strong>With delivery personnel and logistics partners</strong> to fulfill your order
          </p>
        </div>
        <div className="bg-white rounded-xl p-6 border-l-4 border-blue-500 shadow-md">
          <p className="text-gray-700 leading-relaxed">
            <strong>With payment service providers</strong> to process secure transactions
          </p>
        </div>
        <div className="bg-white rounded-xl p-6 border-l-4 border-red-500 shadow-md">
          <p className="text-gray-700 leading-relaxed">
            <strong>With legal authorities</strong> if required by UAE law (e.g. fraud investigation or court orders)
          </p>
        </div>
        <div className="bg-white rounded-xl p-6 border-l-4 border-green-500 shadow-md">
          <p className="text-gray-700 leading-relaxed">
            <strong>With third-party service providers</strong> who help us operate our platform, under strict data confidentiality agreements
          </p>
        </div>
      </div>
      
      <div className="mt-8 bg-red-50 border border-red-200 rounded-xl p-6">
        <p className="text-red-800 font-semibold text-center">
          We do not sell or rent your personal data.
        </p>
      </div>
    </section>
  );
}
