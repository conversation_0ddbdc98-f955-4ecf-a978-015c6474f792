import { User } from "@/types/user";
import { apiRequest } from "@/utils/axios-utils";

export interface LoginCredentials {
  identifier: string;
  password: string;
}

export interface LoginResponse {
  jwt: string;
  user: User;
}

export const loginUser = async (credentials: LoginCredentials): Promise<LoginResponse> => {
  const response = await apiRequest<LoginResponse>({
    url: "/auth/local",
    method: "POST",
    data: credentials,
  });
  return response;
};
