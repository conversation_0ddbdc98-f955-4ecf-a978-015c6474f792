"use client";

import React, { useState } from "react";
import { Package, Truck } from "lucide-react";

import { Skeleton } from "@/components/ui/skeleton";
import { cn } from "@/lib/utils";
import useGetMyPickDropsQuery from "../_hooks/useGetMyPickDropsQuery";
import useGetMyOrdersQuery from "./_hooks/useGetMyOrdersQuery";
import Order from "./components/Order";
import PickDropOrder from "./components/PickDropOrder";

type OrderType = "product" | "pickdrop";

function Orders() {
  const [activeOrderType, setActiveOrderType] = useState<OrderType>("product");

  // Fetch product orders
  const {
    data: productOrders,
    isLoading: isLoadingProductOrders,
    isError: isProductOrdersError,
  } = useGetMyOrdersQuery();

  // Fetch pick & drop orders
  const {
    data: pickDropOrders,
    isLoading: isLoadingPickDropOrders,
    isError: isPickDropOrdersError,
  } = useGetMyPickDropsQuery();

  // Determine if we're loading based on the active tab
  const isLoading = activeOrderType === "product" ? isLoadingProductOrders : isLoadingPickDropOrders;
  const isError = activeOrderType === "product" ? isProductOrdersError : isPickDropOrdersError;

  // Loading state
  if (isLoading) {
    return (
      <div className="max-width-wrapper mb-20 grid grid-cols-1 md:grid-cols-12 gap-6 md:gap-10">
        <div className="hidden md:block md:col-span-4 lg:col-span-3 md:p-4 md:border-r md:border-gray-100">
          <Skeleton className="h-6 w-40 mb-4" />
          <div className="space-y-3">
            {[1, 2, 3, 4].map((i) => (
              <Skeleton key={i} className="h-5 w-full" />
            ))}
          </div>
        </div>
        <div className="w-full rounded-lg p-5 space-y-5 md:col-span-8 lg:col-span-9">
          <div className="space-y-1">
            <Skeleton className="h-7 w-40" />
            <Skeleton className="h-5 w-60" />
          </div>
          <div className="space-y-6">
            {[1, 2, 3].map((i) => (
              <Skeleton key={i} className="h-40 w-full rounded-lg" />
            ))}
          </div>
        </div>
      </div>
    );
  }

  // Error state
  if (isError) {
    return (
      <div className="max-width-wrapper mb-20  text-center py-10">
        <h3 className="text-xl font-bold text-red-600">Failed to load orders</h3>
        <p className="mt-2 text-gray-600">Please try again later</p>
      </div>
    );
  }

  // Get the count of each order type
  const productOrdersCount = productOrders?.length || 0;
  const pickDropOrdersCount = pickDropOrders?.results?.length || 0;

  return (
    <div className="max-width-wrapper mb-20  grid grid-cols-1 md:grid-cols-12 gap-6 md:gap-10">
      {/* Order type section */}
      <div className="hidden md:block md:col-span-4 lg:col-span-3 md:p-4 md:border-r md:border-gray-100">
        <h2 className="mb-4 text-sm font-bold tracking-wide text-[#013356] uppercase">Order Type</h2>
        <ul className="space-y-3">
          <li
            onClick={() => setActiveOrderType("product")}
            className={cn(
              "flex justify-between cursor-pointer transition-colors hover:text-black p-2 rounded-md",
              activeOrderType === "product" ? "bg-blue-50 text-blue-700 font-medium" : "text-gray-500"
            )}
          >
            <div className="flex items-center gap-2">
              <Package size={18} />
              <span className="tracking-tight">Product Orders</span>
            </div>
            <span className="text-sm bg-gray-100 px-2 py-0.5 rounded-full">{productOrdersCount}</span>
          </li>
          <li
            onClick={() => setActiveOrderType("pickdrop")}
            className={cn(
              "flex justify-between cursor-pointer transition-colors hover:text-black p-2 rounded-md",
              activeOrderType === "pickdrop" ? "bg-blue-50 text-blue-700 font-medium" : "text-gray-500"
            )}
          >
            <div className="flex items-center gap-2">
              <Truck size={18} />
              <span className="tracking-tight">Pick & Drop Orders</span>
            </div>
            <span className="text-sm bg-gray-100 px-2 py-0.5 rounded-full">{pickDropOrdersCount}</span>
          </li>
        </ul>

        {/* Order summary for Pick & Drop */}
        {activeOrderType === "pickdrop" && pickDropOrders?.summary && (
          <div className="mt-8 border-t pt-6">
            <h3 className="text-sm font-medium mb-3">Order Status Summary</h3>
            <div className="space-y-2 text-sm">
              <div className="flex justify-between">
                <span className="text-gray-500">Pending</span>
                <span className="font-medium">{pickDropOrders.summary.pending}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-500">Confirmed</span>
                <span className="font-medium">{pickDropOrders.summary.confirmed}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-500">In Transit</span>
                <span className="font-medium">{pickDropOrders.summary.inTransit}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-500">Completed</span>
                <span className="font-medium">{pickDropOrders.summary.completed}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-500">Cancelled</span>
                <span className="font-medium">{pickDropOrders.summary.cancelled}</span>
              </div>
            </div>
          </div>
        )}
      </div>
      {/* Order type section ends */}

      {/* Mobile order type selector */}
      <div className="md:hidden flex gap-2 p-2 mb-4">
        <button
          onClick={() => setActiveOrderType("product")}
          className={cn(
            "flex-1 py-2 px-3 rounded-md text-sm font-medium transition-colors",
            activeOrderType === "product"
              ? "bg-blue-50 text-blue-700 border border-blue-200"
              : "bg-gray-100 text-gray-700"
          )}
        >
          Product Orders ({productOrdersCount})
        </button>
        <button
          onClick={() => setActiveOrderType("pickdrop")}
          className={cn(
            "flex-1 py-2 px-3 rounded-md text-sm font-medium transition-colors",
            activeOrderType === "pickdrop"
              ? "bg-blue-50 text-blue-700 border border-blue-200"
              : "bg-gray-100 text-gray-700"
          )}
        >
          Pick & Drop ({pickDropOrdersCount})
        </button>
      </div>

      <div className="w-full rounded-lg p-5 tracking-tight space-y-5 md:col-span-8 lg:col-span-9">
        {/* Product Orders Content */}
        {activeOrderType === "product" && (
          <>
            {/* header */}
            <div className="space-y-1">
              <h3 className="font-bold text-xl">Product Orders</h3>
              <p className="text-sm text-gray-400 tracking-normal">List of product orders you've made</p>
            </div>
            {/* header ends */}

            {/* orders list */}
            <div className="space-y-6">
              {productOrders && productOrders?.length > 0 ? (
                productOrders.map((order) => (
                  <Order
                    key={order?.id}
                    id={order?.id}
                    orderNumber={order?.documentId}
                    orderStatus={
                      (order?.orderStatus && order?.orderStatus?.length > 0
                        ? order?.orderStatus[0]?.shippingStatus
                        : "Processing") as string
                    }
                    paymentStatus={order?.paymentStatus}
                    createdAt={order?.createdAt}
                    items={order?.products?.map((product) => ({
                      id: product?.product?.documentId,
                      name: product?.product.name,
                      quantity: product?.quantity,
                      price: product?.product?.price,
                      imageSrc: product?.product?.images[0]?.url,
                    }))}
                    deliveryType={order?.deliveryType}
                    deliveryFee={order?.deliveryFee}
                  />
                ))
              ) : (
                <div className="text-center py-10 bg-gray-50 rounded-lg">
                  <Package className="mx-auto h-12 w-12 text-gray-400 mb-3" />
                  <p className="text-gray-500 mb-1">No product orders found</p>
                  <p className="text-sm text-gray-400">Your product orders will appear here</p>
                </div>
              )}
            </div>
            {/* orders list end */}
          </>
        )}

        {/* Pick & Drop Orders Content */}
        {activeOrderType === "pickdrop" && (
          <>
            {/* header */}
            <div className="space-y-1">
              <h3 className="font-bold text-xl">Pick & Drop Orders</h3>
              <p className="text-sm text-gray-400 tracking-normal">List of pick & drop orders you've made</p>
            </div>
            {/* header ends */}

            {/* orders list */}
            <div className="space-y-6">
              {pickDropOrders?.results && pickDropOrders.results.length > 0 ? (
                pickDropOrders.results.map((order) => <PickDropOrder key={order.id} order={order} />)
              ) : (
                <div className="text-center py-10 bg-gray-50 rounded-lg">
                  <Truck className="mx-auto h-12 w-12 text-gray-400 mb-3" />
                  <p className="text-gray-500 mb-1">No pick & drop orders found</p>
                  <p className="text-sm text-gray-400">Your pick & drop orders will appear here</p>
                </div>
              )}
            </div>
            {/* orders list end */}

            {/* Pagination for pick & drop orders */}
            {pickDropOrders?.pagination && pickDropOrders.pagination.pageCount > 1 && (
              <div className="flex justify-center mt-8">
                <div className="flex space-x-1">
                  {Array.from({ length: pickDropOrders.pagination.pageCount }).map((_, i) => (
                    <button
                      key={i}
                      className={cn(
                        "px-3 py-1 rounded text-sm",
                        pickDropOrders.pagination.page === i + 1
                          ? "bg-blue-600 text-white"
                          : "bg-gray-100 text-gray-700 hover:bg-gray-200"
                      )}
                      // Add pagination logic here
                    >
                      {i + 1}
                    </button>
                  ))}
                </div>
              </div>
            )}
          </>
        )}
      </div>
    </div>
  );
}

export default Orders;
