import { Product } from "@/types/product";
import { apiRequest } from "@/utils/axios-utils";

export interface getProductDetailResponse {
  data: Product;
  meta: null;
}

export const getProductDetail = async (productId: number) => {
  const response = await apiRequest<getProductDetailResponse>({
    url: `/products/${productId}`,
    method: "GET",
    showToast: false,
  });
  return response.data;
};
