import { z } from "zod";

export const checkoutFormSchema = z.object({
  shippingAddress: z.object({
    name: z.string().min(2, { message: "Full name is required" }),
    addressLine1: z.string().min(5, { message: "Address line 1 is required" }),
    addressLine2: z.string().optional(),
    apartmentOrVilla: z.enum(["Apartment", "Villa"], {
      message: "Please select apartment or villa",
    }),
    emirate: z.string().min(1, { message: "Please select an emirate" }),
    phoneNumber: z.string().min(10, { message: "Valid phone number is required" }),
  }),
  deliveryType: z.string().min(1, { message: "Delivery type is required" }),
  scheduledDateTime: z.date().nullable(),
  paymentMethod: z.enum(["card", "cash_on_delivery"], {
    message: "Please select a payment method",
  }),
  customerEmail: z.string().email({ message: "Please enter a valid email address" }),
});

export type CheckoutFormValues = z.infer<typeof checkoutFormSchema>;

export const defaultCheckoutValues: Partial<CheckoutFormValues> = {
  shippingAddress: {
    name: "",
    addressLine1: "",
    addressLine2: "",
    apartmentOrVilla: "Apartment",
    emirate: "",
    phoneNumber: "",
  },
  deliveryType: "",
  scheduledDateTime: null,
  paymentMethod: undefined,
  customerEmail: "",
};
