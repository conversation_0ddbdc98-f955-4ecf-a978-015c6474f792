"use client";

import React from "react";
import Link from "next/link";
import { ChevronDown, LogOut, User } from "lucide-react";

import { Button } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Skeleton } from "@/components/ui/skeleton";
import type { User as UserType } from "@/types/user";
import { removeToken } from "@/utils/remove-token";

interface NavUserProps {
  user: UserType;
  userLoading: boolean;
}

export default function NavUser({ user, userLoading }: NavUserProps) {
  const handleLogout = () => {
    removeToken();
    window.location.reload();
  };

  // Show skeleton while loading
  if (userLoading) {
    return <Skeleton className="w-8 h-8 rounded-full" />;
  }

  // If user is logged in, show user dropdown
  if (user) {
    return (
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button variant="outline" size="sm" className="rounded-full !px-1 flex items-center gap-2 hover:bg-gray-100">
            <div className="w-6 h-6 bg-black rounded-full flex items-center justify-center">
              <User size={14} className="text-white" />
            </div>
            <ChevronDown size={14} className="text-gray-500" />
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent align="end" className="w-48">
          <div className="px-2 py-1.5">
            <p className="text-sm font-medium">{user.username}</p>
            <p className="text-xs text-gray-500">{user.email}</p>
          </div>
          {/* <DropdownMenuSeparator /> */}
          {/* <DropdownMenuItem asChild>
            <Link href="/profile" className="flex items-center gap-2 cursor-pointer">
              <User size={16} />
              Profile
            </Link>
          </DropdownMenuItem> */}
          <DropdownMenuSeparator />
          <DropdownMenuItem
            onClick={handleLogout}
            className="flex items-center gap-2 cursor-pointer text-red-600 focus:text-red-600"
          >
            <LogOut size={16} />
            Logout
          </DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenu>
    );
  }

  // If user is not logged in, show login/signup dropdown
  return (
    <Link href={"/login"}>
      <Button variant={"outline"}>Login</Button>
    </Link>
  );
}
