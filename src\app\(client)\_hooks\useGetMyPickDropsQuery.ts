import { useQuery } from "@tanstack/react-query";

import { getMyPickDrops } from "@/services/pickDrop/getMyPickDrops.service";
import { GetMyPickDropsParams } from "@/types/pickDrop";

export const useGetMyPickDropsQuery = (params: GetMyPickDropsParams = {}) => {
  return useQuery({
    queryKey: ["my-pick-drops", params],
    queryFn: () => getMyPickDrops(params),
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
};

export default useGetMyPickDropsQuery;
