import { Eye, Lock, Shield } from "lucide-react";

const securityMeasures = [
  {
    icon: Shield,
    iconColor: "text-red-600",
    title: "Encryption & Security",
    bgGradient: "bg-gradient-to-br from-red-50 to-pink-50",
    borderColor: "border-red-100",
    measures: ["Encrypt sensitive data (e.g. payment information)", "Use secure HTTPS connections"],
  },
  {
    icon: Eye,
    iconColor: "text-blue-600",
    title: "Access Control",
    bgGradient: "bg-gradient-to-br from-blue-50 to-indigo-50",
    borderColor: "border-blue-100",
    measures: ["Limit internal access to personal data", "Regularly audit our systems to prevent unauthorized access"],
  },
];

export default function DataSecuritySection() {
  return (
    <section className="mb-16">
      <div className="flex items-center gap-4 mb-8">
        <div className="flex items-center justify-center w-12 h-12 bg-red-100 rounded-xl">
          <Lock className="w-6 h-6 text-red-600" />
        </div>
        <h2 className="section-title text-gray-900">4. Data Security</h2>
      </div>
      <p className="text-gray-700 mb-8 leading-relaxed">
        Your data security is our top priority. We implement strict measures to:
      </p>

      <div className="grid md:grid-cols-2 gap-6">
        {securityMeasures.map((measure, index) => {
          const IconComponent = measure.icon;
          return (
            <div key={index} className={`${measure.bgGradient} rounded-2xl p-6 border ${measure.borderColor}`}>
              <div className="flex items-center gap-3 mb-4">
                <IconComponent className={`w-6 h-6 ${measure.iconColor}`} />
                <h3 className="font-semibold text-gray-900">{measure.title}</h3>
              </div>
              <ul className="space-y-2 text-gray-700">
                {measure.measures.map((item, itemIndex) => (
                  <li key={itemIndex}>• {item}</li>
                ))}
              </ul>
            </div>
          );
        })}
      </div>
    </section>
  );
}
