import { <PERSON>, Shield, Eye } from "lucide-react";

export default function DataSecuritySection() {
  return (
    <section className="mb-16">
      <div className="flex items-center gap-4 mb-8">
        <div className="flex items-center justify-center w-12 h-12 bg-red-100 rounded-xl">
          <Lock className="w-6 h-6 text-red-600" />
        </div>
        <h2 className="text-3xl font-bold text-gray-900">4. Data Security</h2>
      </div>
      <p className="text-lg text-gray-700 mb-8 leading-relaxed">
        Your data security is our top priority. We implement strict measures to:
      </p>
      
      <div className="grid md:grid-cols-2 gap-6">
        <div className="bg-gradient-to-br from-red-50 to-pink-50 rounded-2xl p-6 border border-red-100">
          <div className="flex items-center gap-3 mb-4">
            <Shield className="w-6 h-6 text-red-600" />
            <h3 className="font-semibold text-gray-900">Encryption & Security</h3>
          </div>
          <ul className="space-y-2 text-gray-700">
            <li>• Encrypt sensitive data (e.g. payment information)</li>
            <li>• Use secure HTTPS connections</li>
          </ul>
        </div>
        
        <div className="bg-gradient-to-br from-blue-50 to-indigo-50 rounded-2xl p-6 border border-blue-100">
          <div className="flex items-center gap-3 mb-4">
            <Eye className="w-6 h-6 text-blue-600" />
            <h3 className="font-semibold text-gray-900">Access Control</h3>
          </div>
          <ul className="space-y-2 text-gray-700">
            <li>• Limit internal access to personal data</li>
            <li>• Regularly audit our systems to prevent unauthorized access</li>
          </ul>
        </div>
      </div>
    </section>
  );
}
