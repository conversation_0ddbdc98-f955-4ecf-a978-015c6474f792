import React from "react";
import Image from "next/image";
import Link from "next/link";
import { ArrowUpRight } from "lucide-react";

import { Button } from "@/components/ui/button";

interface CategoryCardProps {
  id: number;
  title: string;
  image: string;
}

function CategoryCard({ id, title, image }: CategoryCardProps) {
  return (
    <div className="group bg-blue-50  h-[14rem] relative grid grid-cols-2 pt-5 pl-5 overflow-hidden rounded-lg bg-tertiary ">
      {/* bg text */}
      <p className="absolute font-bold transition-all duration-150 ease-in-out pointer-events-none group-hover:scale-110 whitespace-nowrap top-10 left-5 text-3xl md:text-4xl opacity-5">
        {title}
      </p>
      {/* content */}
      <div className="z-10 flex flex-col justify-end w-20 h-full gap-5 py-5">
        <h4 className="font-bold text-xl">{title}</h4>
        <Link href={`/category/${id}`}>
          <Button size={"icon"} className="rounded-full transition-all duration-150 ease-in-out group-hover:rotate-45">
            <ArrowUpRight className="text-white" />
          </Button>
        </Link>
      </div>

      {/* image  */}
      <div className="z-10 flex flex-col aspect-square rounded-full overflow-hidden justify-end h-full transform translate-x-8 translate-y-3">
        <Image
          width={300}
          height={300}
          src={image || "/assets/images/placeholder.png"}
          alt={title}
          className=" h-[13rem] group-hover:scale-110 group-hover:skew-x-3 transition-all duration-150 ease-in-out"
        />
      </div>
    </div>
  );
}

export default CategoryCard;
