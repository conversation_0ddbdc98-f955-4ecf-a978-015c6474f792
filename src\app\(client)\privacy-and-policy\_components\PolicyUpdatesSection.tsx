import { FileText } from "lucide-react";

export default function PolicyUpdatesSection() {
  return (
    <section className="mb-16">
      <div className="flex items-center gap-4 mb-8">
        <div className="flex items-center justify-center w-12 h-12 bg-orange-100 rounded-xl">
          <FileText className="w-6 h-6 text-orange-600" />
        </div>
        <h2 className="text-3xl font-bold text-gray-900">10. Policy Updates</h2>
      </div>
      <div className="bg-orange-50 border border-orange-200 rounded-xl p-6">
        <p className="text-orange-800 leading-relaxed">
          We may update this policy from time to time. Changes will be posted on this page with the "Last Updated" date. Continued use of our platform implies acceptance of the updated policy.
        </p>
      </div>
    </section>
  );
}
