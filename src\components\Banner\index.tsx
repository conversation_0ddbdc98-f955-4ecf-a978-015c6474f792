import React from "react";
import Image from "next/image"; // Import the Image component for better image handling
import Link from "next/link";

import { Button } from "@/components/ui/button";
import { BannerSlide } from "@/types/common";

const Banner = ({ title, description, ctaText, ctaLink, image }: BannerSlide) => {
  return (
    <div className="relative bg-black rounded-xl overflow-hidden h-[30rem]">
      {/* Background Image with Overlay */}
      <div className="absolute inset-0 overflow-hidden">
        <Image src={image} alt={title} layout="fill" objectFit="cover" />
        {/* dark overlay */}
        <div className="absolute inset-0 bg-black opacity-60"></div>
      </div>

      {/* Content */}
      <div
        className={`relative max-w-sm sm:max-w-md md:max-w-lg text-white flex items-center justify-center mx-auto flex-col h-full p-5 ${description ? "gap-4" : "gap-8"}`}
      >
        <h1 className="text-3xl sm:text-4xl font-bold tracking-tight text-center leading-[130%]">{title}</h1>
        {description && <p className="text-gray-200 text-center">{description}</p>}
        <Link href={ctaLink}>
          <Button size={"lg"} variant={"destructive"} className="rounded-full ">
            {ctaText}
          </Button>
        </Link>
      </div>
    </div>
  );
};

export default Banner;
