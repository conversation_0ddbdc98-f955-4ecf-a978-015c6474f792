import { FileText } from "lucide-react";

export default function DataRetentionSection() {
  return (
    <section className="mb-16">
      <div className="flex items-center gap-4 mb-8">
        <div className="flex items-center justify-center w-12 h-12 bg-teal-100 rounded-xl">
          <FileText className="w-6 h-6 text-teal-600" />
        </div>
        <h2 className="section-title text-gray-900">7. Data Retention</h2>
      </div>
      <p className="text-gray-700 mb-6 leading-relaxed">We retain your data only as long as necessary for:</p>

      <div className="space-y-4">
        <div className="bg-white rounded-xl p-6 border-l-4 border-teal-500 shadow-md">
          <p className="text-gray-700">Fulfilling orders and customer service</p>
        </div>
        <div className="bg-white rounded-xl p-6 border-l-4 border-blue-500 shadow-md">
          <p className="text-gray-700">Legal and regulatory compliance</p>
        </div>
        <div className="bg-white rounded-xl p-6 border-l-4 border-purple-500 shadow-md">
          <p className="text-gray-700">Internal analytics (with anonymized data if possible)</p>
        </div>
      </div>
    </section>
  );
}
