"use client";

import React from "react";

import ProductCard from "@/components/ProductCard";
import { Skeleton } from "@/components/ui/skeleton";
import { Product } from "@/types/product";

interface ProductListProps {
  products: Product[];
  isLoading: boolean;
  isError: boolean;
}

function ProductList({ products, isLoading, isError }: ProductListProps) {
  const productsEmpty = products.length === 0;

  if (isLoading) {
    return (
      <div className="md:col-span-8 lg:col-span-9 grid grid-cols-2 gap-4 lg:grid-cols-3 h-fit">
        {Array(6)
          .fill(0)
          .map((_, index) => (
            <div key={index} className="space-y-3">
              <Skeleton className="h-[13rem] w-full rounded-xl" />
              <Skeleton className="h-4 w-2/3" />
              <Skeleton className="h-4 w-1/3" />
            </div>
          ))}
      </div>
    );
  }

  if (isError) {
    return (
      <div className="md:col-span-8 lg:col-span-9 flex items-center justify-center py-10">
        <p className="text-red-500">Failed to load products. Please try again later.</p>
      </div>
    );
  }

  if (productsEmpty) {
    return (
      <div className="md:col-span-8 lg:col-span-9 flex items-center justify-center h-[30rem]">
        <p className="text-gray-500">No products found matching your criteria.</p>
      </div>
    );
  }

  return (
    <div className="md:col-span-8 lg:col-span-9 grid grid-cols-2 gap-4 lg:grid-cols-3 h-fit">
      {products.map((product) => (
        <ProductCard
          key={product.id}
          name={product.name}
          image={product.images[0]?.url}
          price={product.price}
          stock={product.stock}
          ratings={product.ratings}
          _id={product.id}
        />
      ))}
    </div>
  );
}

export default ProductList;
