import { Shield } from "lucide-react";

export default function PrivacyAndPolicyHeroSection() {
  return (
    <div className="bg-gradient-to-r from-[#0c1922] via-[#013356] to-[#0c1922] text-white h-[20rem] flex-center">
      <div className="max-width-wrapper">
        <div className="text-center">
          <div className="inline-flex items-center justify-center w-16 h-16 bg-white/20 rounded-full mb-6 backdrop-blur-sm">
            <Shield className="w-8 h-8" />
          </div>
          <h1 className="text-3xl sm:text-4xl font-bold mb-4 tracking-tight">Privacy Policy</h1>
          <p className="text-blue-100 max-w-3xl mx-auto leading-relaxed">
            <strong>EEF EXPRESS</strong> - Your privacy is important to us. We are committed to complying with the UAE's
            Personal Data Protection Law and applicable international data privacy standards.
          </p>
        </div>
      </div>
    </div>
  );
}
