import { Shield } from "lucide-react";

export default function PrivacyAndPolicyHeroSection() {
  return (
    <div className="bg-gradient-to-r from-blue-600 via-purple-600 to-indigo-700 text-white">
      <div className="max-width-wrapper py-16 lg:py-24">
        <div className="text-center">
          <div className="inline-flex items-center justify-center w-16 h-16 bg-white/20 rounded-full mb-6 backdrop-blur-sm">
            <Shield className="w-8 h-8" />
          </div>
          <h1 className="text-4xl lg:text-6xl font-bold mb-4 tracking-tight">Privacy Policy</h1>
          <p className="text-xl lg:text-2xl text-blue-100 max-w-3xl mx-auto leading-relaxed">
            <strong>EEF EXPRESS</strong> - Your privacy is important to us. This Privacy Policy explains how we collect,
            use, store, and protect your personal information.
          </p>
          <div className="mt-8 text-sm text-blue-200">
            We are committed to complying with the UAE's Personal Data Protection Law and applicable international data
            privacy standards.
          </div>
        </div>
      </div>
    </div>
  );
}
