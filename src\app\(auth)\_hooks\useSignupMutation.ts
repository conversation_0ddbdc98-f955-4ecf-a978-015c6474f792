"use client";

import { useRouter } from "next/navigation";
import { useMutation } from "@tanstack/react-query";
import { toast } from "sonner";

import { registerUser, RegistrationResponse, signupFormData } from "@/services/auth/signup.service";
import { saveToken } from "@/utils/save-token";

export const useSignupMutation = () => {
  const router = useRouter();

  return useMutation({
    mutationFn: (data: signupFormData) => registerUser(data),
    onSuccess: (response: RegistrationResponse) => {
      toast.success("User registered successfully.");
      toast.info("Logging you in...");
      saveToken(response.jwt);
      router.push("/");
    },
  });
};
