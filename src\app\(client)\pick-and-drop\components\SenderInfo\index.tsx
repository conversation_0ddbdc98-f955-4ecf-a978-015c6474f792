import React from "react";
import { Control } from "react-hook-form";

import { FormControl, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { PickDropFormValues } from "@/lib/schemas/pickDropFormSchema";

interface SenderInfoProps {
  control: Control<PickDropFormValues>;
}

function SenderInfo({ control }: SenderInfoProps) {
  return (
    <div className="w-full border rounded-lg p-5 tracking-tight space-y-5">
      {/* header  */}
      <div className="mb-8 flex justify-between items-end">
        <div className="space-y-1">
          <h3 className="font-bold text-xl ">Sender's Info</h3>
          <p className="text-sm text-gray-400 tracking-normal">Please enter sender's details</p>
        </div>

        <p className="text-gray-400 text-sm">Step 1 of 6</p>
      </div>
      {/* header ends */}

      {/* body  */}

      <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
        {/* input group  */}
        <FormField
          control={control}
          name="senderName"
          render={({ field }) => (
            <FormItem className="space-y-2.5">
              <FormLabel>Sender Name</FormLabel>
              <FormControl>
                <Input placeholder="John Doe" {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
        {/* input group  */}
        <FormField
          control={control}
          name="senderPhoneNumber"
          render={({ field }) => (
            <FormItem className="space-y-2.5">
              <FormLabel>Sender's phone number</FormLabel>
              <FormControl>
                <Input placeholder="****** 456 7890" {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
      </div>

      <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
        {/* input group  */}
        <FormField
          control={control}
          name="senderAddressLine1"
          render={({ field }) => (
            <FormItem className="space-y-2.5">
              <FormLabel>Address Line 1</FormLabel>
              <FormControl>
                <Input placeholder="123 Main St" {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
        {/* input group  */}
        <FormField
          control={control}
          name="senderAddressLine2"
          render={({ field }) => (
            <FormItem className="space-y-2.5">
              <FormLabel>Address Line 2</FormLabel>
              <FormControl>
                <Input placeholder="Apartment, suite, etc." {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
      </div>
    </div>
  );
}

export default SenderInfo;
