"use client";

import React, { useEffect } from "react";
import { useSearchParams } from "next/navigation";
import { toast } from "sonner";

import LoginForm from "../LoginForm";

export default function LoginContent() {
  const searchParams = useSearchParams();
  const returnUrl = searchParams?.get("returnUrl");

  useEffect(() => {
    // Show toast notification if user was redirected from a protected route
    if (returnUrl) {
      toast.info("You need to be logged in to access this page.");
    }
  }, [returnUrl]);

  return <LoginForm />;
}
