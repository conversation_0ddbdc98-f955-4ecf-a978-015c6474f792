"use client";

import React, { useState } from "react";
import Image from "next/image";
import { Plus, X } from "lucide-react";

import { Button } from "@/components/ui/button";
import { useRemoveItemFromCartMutation } from "@/hooks/cart/useRemoveItemFromCartMutation";
import { useUpdateItemQuantityInCartMutation } from "@/hooks/cart/useUpdateItemQuantityInCartMutation";
import type { CartItem } from "@/types/cart";
import { getApiImage } from "@/utils/getApiImage";
import LoadingSpinner from "../LoadingSpinner";

interface CartItemProps {
  item: CartItem;
}

export default function CartItem({ item }: CartItemProps) {
  const [isIncrementing, setIsIncrementing] = useState(false);
  const [isDecrementing, setIsDecrementing] = useState(false);

  const updateQuantityMutation = useUpdateItemQuantityInCartMutation();
  const removeItemMutation = useRemoveItemFromCartMutation();

  const incrementQuantity = () => {
    setIsIncrementing(true);
    updateQuantityMutation.mutate(
      {
        itemId: item.product.id,
        quantity: item.quantity + 1,
      },
      {
        onSettled: () => {
          setIsIncrementing(false);
        },
      }
    );
  };

  const decrementQuantity = () => {
    if (item.quantity > 1) {
      setIsDecrementing(true);
      updateQuantityMutation.mutate(
        {
          itemId: item.product.id,
          quantity: item.quantity - 1,
        },
        {
          onSettled: () => {
            setIsDecrementing(false);
          },
        }
      );
    }
  };

  const handleRemoveItem = (productId: number) => {
    removeItemMutation.mutate(productId);
  };

  return (
    <div className="relative flex items-center space-x-4 border-b last:border-b-0 p-5 lg:space-x-6">
      <Button
        variant="destructive"
        size="icon"
        className="absolute top-1 left-0 z-10 w-5 h-5 p-1 rounded-full"
        onClick={() => handleRemoveItem(item.product.id)}
        disabled={removeItemMutation.isPending}
      >
        {removeItemMutation.isPending ? <LoadingSpinner size="xs" /> : <X size={10} />}
      </Button>

      <div className="relative w-16 h-16 rounded-md overflow-hidden bg-gray-50">
        <Image
          src={getApiImage(item.product.images[0]?.url) || "/placeholder.png"}
          alt={item.product.name}
          layout="fill"
          objectFit="cover"
          className="bg-gray-50"
        />
      </div>

      <div className="flex-1">
        <h6 className="font-medium text-gray-900">{item.product.name}</h6>
      </div>

      <div className="space-y-2">
        <p className="text-sm text-gray-500">{item.product.price} AED</p>

        <div className="flex items-center space-x-2 mt-2 text-sm border p-1.5 rounded-full">
          <Button
            variant="ghost"
            size="icon"
            className="w-7 h-7 p-0 rounded-full hover:bg-black hover:text-white"
            onClick={decrementQuantity}
            disabled={item.quantity <= 1 || isDecrementing || isIncrementing}
          >
            {isDecrementing ? <LoadingSpinner size="xs" /> : "-"}
          </Button>
          <span>{item.quantity}</span>
          <Button
            variant="ghost"
            size="icon"
            className="w-7 h-7 p-0 rounded-full hover:bg-black hover:text-white"
            onClick={incrementQuantity}
            disabled={isIncrementing || isDecrementing}
          >
            {isIncrementing ? <LoadingSpinner size="sm" /> : <Plus size={10} />}
          </Button>
        </div>
      </div>
    </div>
  );
}
