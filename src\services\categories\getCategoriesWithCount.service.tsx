import { Category } from "@/types/category";
import { apiRequest } from "@/utils/axios-utils";

export interface getCategoryWithCountResponse {
  data: Category[];
  meta: null;
}

export const getCategoryWithCount = async () => {
  const response = await apiRequest<getCategoryWithCountResponse>({
    url: "/categories/with-counts",
    method: "GET",
    showToast: false,
  });
  return response.data;
};
