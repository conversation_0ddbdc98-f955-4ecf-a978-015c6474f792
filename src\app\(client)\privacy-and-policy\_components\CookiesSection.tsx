import { Eye, FileText, Globe, Users } from "lucide-react";

const cookieUsages = [
  {
    icon: FileText,
    iconColor: "text-indigo-600",
    bgColor: "bg-indigo-100",
    description: "Store your session and cart details",
  },
  {
    icon: Eye,
    iconColor: "text-green-600",
    bgColor: "bg-green-100",
    description: "Analyze traffic and user behavior",
  },
  {
    icon: Users,
    iconColor: "text-blue-600",
    bgColor: "bg-blue-100",
    description: "Improve user experience",
  },
];

export default function CookiesSection() {
  return (
    <section className="mb-16">
      <div className="flex items-center gap-4 mb-8">
        <div className="flex items-center justify-center w-12 h-12 bg-indigo-100 rounded-xl">
          <Globe className="w-6 h-6 text-indigo-600" />
        </div>
        <h2 className="section-title text-gray-900">6. Cookies and Tracking Technologies</h2>
      </div>
      <p className="text-gray-700 mb-6 leading-relaxed">We use cookies to:</p>

      <div className="grid md:grid-cols-3 gap-4 mb-8">
        {cookieUsages.map((usage, index) => {
          const IconComponent = usage.icon;
          return (
            <div key={index} className="bg-white rounded-xl p-6 shadow-md border border-gray-100 text-center">
              <div className={`w-12 h-12 ${usage.bgColor} rounded-full flex items-center justify-center mx-auto mb-4`}>
                <IconComponent className={`w-6 h-6 ${usage.iconColor}`} />
              </div>
              <p className="text-gray-700">{usage.description}</p>
            </div>
          );
        })}
      </div>

      <div className="bg-amber-50 border border-amber-200 rounded-xl p-6">
        <p className="text-amber-800 leading-relaxed">
          You can control cookie settings in your browser. However, disabling cookies may affect your experience on our
          platform.
        </p>
      </div>
    </section>
  );
}
