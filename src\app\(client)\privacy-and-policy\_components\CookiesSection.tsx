import { Eye, FileText, Globe, Users } from "lucide-react";

export default function CookiesSection() {
  return (
    <section className="mb-16">
      <div className="flex items-center gap-4 mb-8">
        <div className="flex items-center justify-center w-12 h-12 bg-indigo-100 rounded-xl">
          <Globe className="w-6 h-6 text-indigo-600" />
        </div>
        <h2 className="section-title text-gray-900">6. Cookies and Tracking Technologies</h2>
      </div>
      <p className="text-gray-700 mb-6 leading-relaxed">We use cookies to:</p>

      <div className="grid md:grid-cols-3 gap-4 mb-8">
        <div className="bg-white rounded-xl p-6 shadow-md border border-gray-100 text-center">
          <div className="w-12 h-12 bg-indigo-100 rounded-full flex items-center justify-center mx-auto mb-4">
            <FileText className="w-6 h-6 text-indigo-600" />
          </div>
          <p className="text-gray-700">Store your session and cart details</p>
        </div>
        <div className="bg-white rounded-xl p-6 shadow-md border border-gray-100 text-center">
          <div className="w-12 h-12 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
            <Eye className="w-6 h-6 text-green-600" />
          </div>
          <p className="text-gray-700">Analyze traffic and user behavior</p>
        </div>
        <div className="bg-white rounded-xl p-6 shadow-md border border-gray-100 text-center">
          <div className="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
            <Users className="w-6 h-6 text-blue-600" />
          </div>
          <p className="text-gray-700">Improve user experience</p>
        </div>
      </div>

      <div className="bg-amber-50 border border-amber-200 rounded-xl p-6">
        <p className="text-amber-800 leading-relaxed">
          You can control cookie settings in your browser. However, disabling cookies may affect your experience on our
          platform.
        </p>
      </div>
    </section>
  );
}
