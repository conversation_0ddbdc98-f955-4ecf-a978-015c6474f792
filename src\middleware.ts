import { NextRequest, NextResponse } from "next/server";

// Protected routes that require authentication
const protectedRoutes = ["/checkout", "/orders", "/pick-and-drop", "/account"];

// Auth routes (redirect to home if already logged in)
const authRoutes = ["/login", "/signup"];

export default async function middleware(req: NextRequest) {
  // Get the pathname from the URL
  const path = req.nextUrl.pathname;

  // Check if the current route is protected
  const isProtectedRoute = protectedRoutes.some((route) => path === route || path.startsWith(`${route}/`));

  // Check if the current route is an auth route
  const isAuthRoute = authRoutes.some((route) => path === route || path.startsWith(`${route}/`));

  // Check for authentication token
  const token = req.cookies.get("EEF_AUTH_TOKEN")?.value;
  const isAuthenticated = !!token;

  // If trying to access a protected route without authentication
  if (isProtectedRoute && !isAuthenticated) {
    // Redirect to login page with return URL
    const returnUrl = encodeURIComponent(req.nextUrl.pathname);
    return NextResponse.redirect(new URL(`/login?returnUrl=${returnUrl}`, req.url));
  }

  // If already authenticated and trying to access login/signup pages
  if (isAuthRoute && isAuthenticated) {
    // Redirect to home page
    return NextResponse.redirect(new URL("/", req.url));
  }

  // Allow the request to continue
  return NextResponse.next();
}

// Configure which routes the middleware should run on
export const config = {
  matcher: [
    // Match all routes except static files, api routes, etc.
    "/((?!api|_next/static|_next/image|favicon.ico|assets).*)",
  ],
};
