"use client";

import React from "react";
// import Image from "next/image";
import Link from "next/link";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import { z } from "zod";

import { But<PERSON> } from "@/components/ui/button";
import { Form, FormControl, FormField, FormItem, FormMessage } from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { useSignupMutation } from "../../_hooks/useSignupMutation";

// Zod schema for validation
const signupSchema = z.object({
  username: z
    .string()
    .min(3, "Username must be at least 3 characters")
    .max(20, "Username must be less than 20 characters"),
  email: z.string().email("Please enter a valid email address").min(1, "Email is required"),
  password: z
    .string()
    .min(8, "Password must be at least 8 characters")
    .regex(
      /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/,
      "Password must contain at least one uppercase letter, one lowercase letter, and one number"
    ),
});

type SignupFormValues = z.infer<typeof signupSchema>;

function SignupForm() {
  const signupMutation = useSignupMutation();

  const form = useForm<SignupFormValues>({
    resolver: zodResolver(signupSchema),
    defaultValues: {
      username: "",
      email: "",
      password: "",
    },
  });

  const onSubmit = (values: SignupFormValues) => {
    const signupData = {
      username: values.username,
      email: values.email,
      password: values.password,
    };

    signupMutation.mutate(signupData);
  };

  return (
    <div className="text-center p-4 sm:p-8 rounded-lg shadow-lg w-full max-w-sm sm:max-w-md">
      {/* header */}
      <div className="space-y-2">
        <h1 className="text-2xl font-bold font-avantGarde tracking-tight">Create an account</h1>
        <p className="text-sm sm:text-base text-gray-500">Enter your details below to create an account</p>
      </div>
      {/* header ends */}

      {/* body */}
      <div className="flex flex-col gap-2.5 mt-4">
        {/* <Button variant="outline" size={"lg"} className="w-full">
          <Image src={"/assets/svgs/google.svg"} height={20} width={20} alt="google" />
          Signup with Google
        </Button>

        <div className="my-4 relative text-center text-sm after:absolute after:inset-0 after:top-1/2 after:z-0 after:flex after:items-center after:border-t after:border-border">
          <span className="relative z-10 bg-white px-3 text-gray-500 text-sm sm:text-base">OR</span>
        </div> */}

        {/* form fields */}
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-2.5">
            <FormField
              control={form.control}
              name="username"
              render={({ field }) => (
                <FormItem>
                  <FormControl>
                    <Input className="text-sm sm:text-base" type="text" placeholder="User Name" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="email"
              render={({ field }) => (
                <FormItem>
                  <FormControl>
                    <Input className="text-sm sm:text-base" type="email" placeholder="Email" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="password"
              render={({ field }) => (
                <FormItem>
                  <FormControl>
                    <Input className="text-sm sm:text-base" type="password" placeholder="Password" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <Button
              size={"lg"}
              className="text-sm sm:text-base w-full"
              type="submit"
              disabled={signupMutation.isPending}
            >
              {signupMutation.isPending ? "Signing up..." : "Sign up"}
            </Button>
          </form>
        </Form>
        {/* form fields ends */}

        <div className="text-center text-sm">
          Have an account already?
          <Link href={"login"} className="underline ml-1 font-medium hover:text-blue-500">
            Login
          </Link>
        </div>
      </div>
      {/* body ends */}
    </div>
  );
}

export default SignupForm;
