import { Target } from "lucide-react";

export default function VisionSection() {
  return (
    <section className="mb-16">
      <div className="flex items-center gap-4 mb-8">
        <div className="flex items-center justify-center w-12 h-12 bg-purple-100 rounded-xl">
          <Target className="w-6 h-6 text-purple-600" />
        </div>
        <h2 className="section-title text-gray-900">🌍 Our Vision</h2>
      </div>
      
      <div className="bg-gradient-to-r from-purple-600 via-indigo-600 to-blue-600 text-white rounded-2xl p-8 text-center">
        <div className="max-w-3xl mx-auto">
          <div className="inline-flex items-center justify-center w-16 h-16 bg-white/20 rounded-full mb-6 backdrop-blur-sm">
            <Target className="w-8 h-8" />
          </div>
          <p className="text-xl leading-relaxed">
            To become the UAE's most trusted digital delivery and shopping platform — connecting people, products, and services with unmatched speed and reliability.
          </p>
        </div>
      </div>
    </section>
  );
}
