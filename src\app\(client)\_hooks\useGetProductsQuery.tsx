import { keepPreviousData, useQuery } from "@tanstack/react-query";

import { getProducts, ProductsFilterParams } from "@/services/products/getProducts.service";

const useGetProductsQuery = (params: ProductsFilterParams = {}) => {
  return useQuery({
    queryKey: ["products", params],
    queryFn: () => getProducts(params),
    placeholderData: keepPreviousData, // Keep previous data while fetching new data (useful for pagination)
  });
};

export default useGetProductsQuery;
