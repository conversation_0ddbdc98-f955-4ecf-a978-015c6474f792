"use client";

import { useState } from "react";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";

import { defaultValues, pickDropFormSchema, PickDropFormValues } from "@/lib/schemas/pickDropFormSchema";

export interface UsePickDropFormProps {
  onSubmit: (data: PickDropFormValues) => Promise<void>;
}

export const usePickDropForm = ({ onSubmit }: UsePickDropFormProps) => {
  const [isSubmitting, setIsSubmitting] = useState(false);

  const form = useForm<PickDropFormValues>({
    resolver: zodResolver(pickDropFormSchema),
    defaultValues,
    mode: "onChange",
  });

  const handleSubmit = async (data: PickDropFormValues) => {
    try {
      setIsSubmitting(true);
      await onSubmit(data);
      // Don't reset the form here - let the parent component decide
    } catch {
      return;
    } finally {
      setIsSubmitting(false);
    }
  };

  return {
    form,
    isSubmitting,
    handleSubmit: form.handleSubmit(handleSubmit),
  };
};
