import React from "react";
import Image from "next/image";
import Link from "next/link";

import { footerSections } from "@/lib/constants";

export default function Footer() {
  return (
    <footer className="bg-gray-50 py-14 text-white">
      <div className="max-width-wrapper">
        {/* footer links grid */}
        <div className="mt-16 grid gap-8 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-12 ">
          <div className="logo space-y-2 lg:col-span-6">
            <Image src="/assets/svgs/logo.svg" width={100} height={80} alt="eefexpress" />
            <p className="text-sm text-gray-500 max-w-60">Empowering your shopping and delivery needs, effortlessly</p>
          </div>

          {footerSections.map((section, index) => (
            <div key={index} className="flex flex-col gap-3 lg:col-span-2">
              <h4 className="font-avantGarde font-bold tracking-tight text-gray-800">{section.title}</h4>
              {section.links.map((link, linkIndex) =>
                link.outLink ? (
                  <Link key={linkIndex} className="link text-sm text-gray-500" target="_blank" href={link.link}>
                    {link.text}
                  </Link>
                ) : (
                  <Link key={linkIndex} className="link text-sm text-gray-500" href={link.link}>
                    {link.text}
                  </Link>
                )
              )}
            </div>
          ))}
        </div>
      </div>
    </footer>
  );
}
