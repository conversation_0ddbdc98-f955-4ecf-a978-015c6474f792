import { User } from "@/types/user";
import { apiRequest } from "@/utils/axios-utils";

// this will be moved from here
export interface signupFormData {
  username: string;
  email: string;
  password: string;
}

export interface RegistrationResponse {
  jwt: string;
  user: User;
}
// Auth functions
export const registerUser = async (signupFormData: signupFormData): Promise<RegistrationResponse> => {
  const response = await apiRequest<RegistrationResponse>({
    url: "/auth/local/register",
    method: "POST",
    data: signupFormData,
    showToast: true,
  });
  return response;
};
