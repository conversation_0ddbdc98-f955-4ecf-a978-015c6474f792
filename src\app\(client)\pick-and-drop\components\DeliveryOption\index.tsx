import React from "react";
import { Control } from "react-hook-form";

import useGetDeliveryTypesQuery from "@/app/(client)/_hooks/useGetDeliveryTypesQuery";
import { DateTimePicker24h } from "@/components/ui/datetime-picker";
import { FormControl, FormField, FormItem, FormMessage } from "@/components/ui/form";
import { Label } from "@/components/ui/label";
import { Skeleton } from "@/components/ui/skeleton";
import { PickDropFormValues } from "@/lib/schemas/pickDropFormSchema";
import { cn } from "@/lib/utils";

interface DeliveryOptionProps {
  control: Control<PickDropFormValues>;
  currentStep: number;
  totalSteps: number;
}

function DeliveryOption({ control, currentStep, totalSteps }: DeliveryOptionProps) {
  // Fetch delivery types from API
  const { data: deliveryTypes, isLoading, error } = useGetDeliveryTypesQuery();

  return (
    <div className="w-full border rounded-lg p-5 tracking-tight space-y-5">
      {/* header */}
      <div className="mb-8 flex justify-between items-end">
        <div className="space-y-1">
          <h3 className="font-bold text-xl">Delivery Option</h3>
          <p className="text-sm text-gray-400 tracking-normal">Please select the type of delivery you want</p>
        </div>

        <p className="text-gray-400 text-sm">
          Step {currentStep} of {totalSteps}
        </p>
      </div>
      {/* header ends */}

      {/* body */}
      {isLoading ? (
        // Loading state
        <div className="space-y-4">
          {[1, 2, 3, 4].map((i) => (
            <div key={i} className="rounded-lg border p-4">
              <div className="flex items-start space-x-3">
                <Skeleton className="h-4 w-4 rounded-full mt-1" />
                <div className="flex-1">
                  <div className="flex justify-between items-center">
                    <Skeleton className="h-5 w-40" />
                    <Skeleton className="h-4 w-16" />
                  </div>
                  <Skeleton className="h-4 w-full mt-1" />
                </div>
              </div>
            </div>
          ))}
        </div>
      ) : error ? (
        // Error state
        <div className="p-4 border border-red-200 bg-red-50 rounded-lg text-red-600">
          Failed to load delivery options. Please try again later.
        </div>
      ) : (
        // When data is available
        <FormField
          control={control}
          name="deliveryType"
          render={({ field }) => (
            <FormItem className="space-y-4">
              <div className="space-y-4">
                {deliveryTypes?.map((deliveryType) => {
                  const isSelected = field.value === deliveryType.type;

                  return (
                    <div
                      key={deliveryType.id}
                      className={cn(
                        "rounded-lg border p-4 cursor-pointer hover:border-gray-400 transition-colors",
                        isSelected && "bg-gray-50 border-gray-500"
                      )}
                      onClick={() => field.onChange(deliveryType.type)}
                    >
                      <div className="flex items-start space-x-3">
                        {/* CUSTOM RADIO BUTTON START */}
                        <div className="relative flex h-4 w-4 items-center justify-center mt-1">
                          <div
                            className={cn(
                              "h-4 w-4 rounded-full border border-gray-300",
                              isSelected && "border-2 border-black"
                            )}
                          >
                            {isSelected && (
                              <div className="absolute inset-0 flex items-center justify-center">
                                <div className="h-2 w-2 rounded-full bg-black"></div>
                              </div>
                            )}
                          </div>
                        </div>
                        {/* CUSTOM RADIO BUTTON END */}

                        <div className="flex-1">
                          <div className="flex justify-between items-center">
                            <Label className="font-medium text-base cursor-pointer">{deliveryType.type} Delivery</Label>
                            <span className="text-sm font-medium text-gray-500">{deliveryType.amount} AED</span>
                          </div>
                          <p className="text-sm text-gray-500 mt-1">
                            {deliveryType.description}. {deliveryType.estimatedTime}.
                          </p>

                          {/* DateTime Picker for Scheduled delivery */}
                          {deliveryType.type === "Scheduled" && (
                            <div
                              className={cn(
                                "mt-4 overflow-hidden transition-all duration-300 ease-in-out",
                                isSelected ? "max-h-40 opacity-100" : "max-h-0 opacity-0"
                              )}
                              style={{ pointerEvents: isSelected ? "auto" : "none" }}
                              onClick={(e) => e.stopPropagation()}
                            >
                              <FormField
                                control={control}
                                name="scheduledDateTime"
                                render={({ field: dateTimeField }) => (
                                  <FormItem className="space-y-2.5">
                                    <Label>Scheduled Date & Time</Label>
                                    <FormControl>
                                      <DateTimePicker24h
                                        value={dateTimeField.value || undefined}
                                        onChange={dateTimeField.onChange}
                                      />
                                    </FormControl>
                                    <FormMessage />
                                  </FormItem>
                                )}
                              />
                            </div>
                          )}
                        </div>
                      </div>
                    </div>
                  );
                })}
              </div>
              <FormMessage />
            </FormItem>
          )}
        />
      )}
    </div>
  );
}

export default DeliveryOption;
