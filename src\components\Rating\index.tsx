import React from "react";
import { StarIcon } from "lucide-react";

function Rating({ rating }: { rating: number }) {
  return (
    <div className="flex gap-1 items-center p-1 px-4 border rounded-full">
      <StarIcon className="w-4 h-4" stroke="none" fill="#000" />
      {/* <StarIcon className="w-4 h-4" stroke="none" fill="#fbbf24" /> */}
      <span>{rating}</span>
    </div>
  );
}

export default Rating;
