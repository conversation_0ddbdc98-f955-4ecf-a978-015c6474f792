// "use client";

// import { Form } from "@/components/ui/form";
// import { usePickDropForm, UsePickDropFormProps } from "@/hooks/usePickDropForm";
// import { ConfirmationSection } from "../Confirmation";
// import { DeliveryOptionsSection } from "../DeliveryOption";
// import { ReceiverInfo } from "../ReceiverInfo";
// import { SenderInfo } from "../SenderInfo";

// export interface PickDropFormProps extends UsePickDropFormProps {
//   confirm: boolean;
// }

// export const PickDropForm = ({ onSubmit }: PickDropFormProps) => {
//   const { form, isSubmitting, handleSubmit } = usePickDropForm({ onSubmit });

//   return (
//     <Form {...form}>
//       <form onSubmit={handleSubmit} className="space-y-6">
//         <SenderInfo control={form.control} />
//         <ReceiverInfo control={form.control} />
//         {/* <PickupInfo control={form.control} /> */}
//         {/* <DropoffSection control={form.control} /> */}
//         <DeliveryOptionsSection control={form.control} />
//         <ConfirmationSection control={form.control} isSubmitting={isSubmitting} />
//       </form>
//     </Form>
//   );
// };
