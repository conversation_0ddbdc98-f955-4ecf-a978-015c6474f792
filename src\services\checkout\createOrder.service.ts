import { CheckoutFormValues } from "@/lib/schemas/checkoutFormSchema";
import { CreateOrderResponse, OrderData } from "@/types/order";
import { apiRequest } from "@/utils/axios-utils";

export const createOrder = async (checkoutData: CheckoutFormValues): Promise<OrderData> => {
  const response = await apiRequest<CreateOrderResponse>({
    url: "/checkout",
    method: "POST",
    data: checkoutData,
    showToast: false,
  });

  return response.data;
};
