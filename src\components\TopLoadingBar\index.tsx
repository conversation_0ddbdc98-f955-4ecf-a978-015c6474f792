"use client";

import React, { useEffect, useState } from "react";

interface TopLoadingBarProps {
  isLoading: boolean;
}

const TopLoadingBar: React.FC<TopLoadingBarProps> = ({ isLoading }) => {
  const [progress, setProgress] = useState(0);

  useEffect(() => {
    let interval: NodeJS.Timeout;

    if (isLoading) {
      // Reset progress when loading starts
      setProgress(0);

      // Simulate progress
      interval = setInterval(() => {
        setProgress((prevProgress) => {
          // Slowly increase up to 90% while loading
          if (prevProgress < 90) {
            return prevProgress + Math.random() * 10;
          }
          return prevProgress;
        });
      }, 200);
    } else {
      // When loading completes, quickly go to 100%
      setProgress(100);

      // Reset after animation completes
      const timeout = setTimeout(() => {
        setProgress(0);
      }, 500);

      return () => clearTimeout(timeout);
    }

    return () => {
      if (interval) clearInterval(interval);
    };
  }, [isLoading]);

  return (
    <div
      className="fixed top-0 left-0 z-50 h-[3px] bg-red-700 transition-all duration-300 ease-out"
      style={{
        width: `${progress}%`,
        opacity: progress > 0 ? 1 : 0,
      }}
    />
  );
};

export default TopLoadingBar;
