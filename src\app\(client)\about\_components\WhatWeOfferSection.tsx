import { Eye, Headphones, ShoppingBag, Truck, UtensilsCrossed } from "lucide-react";

const services = [
  {
    icon: UtensilsCrossed,
    title: "Food Delivery",
    description: "Browse top local restaurants, place your order, and get it delivered fresh to your door.",
    bgColor: "bg-orange-100",
    iconColor: "text-orange-600",
    borderColor: "border-orange-200",
  },
  {
    icon: ShoppingBag,
    title: "E-Commerce Shopping",
    description: "Shop a wide range of products — from fashion to electronics — with fast delivery.",
    bgColor: "bg-green-100",
    iconColor: "text-green-600",
    borderColor: "border-green-200",
  },
  {
    icon: Truck,
    title: "Pick-up & Drop-off Services",
    description: "Schedule pick-ups or deliveries of packages within your emirate or across the UAE.",
    bgColor: "bg-blue-100",
    iconColor: "text-blue-600",
    borderColor: "border-blue-200",
  },
  {
    icon: Eye,
    title: "Live Order Tracking",
    description: "Know exactly where your order is at any moment.",
    bgColor: "bg-purple-100",
    iconColor: "text-purple-600",
    borderColor: "border-purple-200",
  },
  {
    icon: Headphones,
    title: "Customer-Centered Service",
    description: "Get support when you need it, and enjoy easy returns and secure payments.",
    bgColor: "bg-pink-100",
    iconColor: "text-pink-600",
    borderColor: "border-pink-200",
  },
];

export default function WhatWeOfferSection() {
  return (
    <section className="mb-16">
      <div className=" mb-8">
        <h2 className="section-title text-gray-900">What We Offer</h2>
      </div>

      <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
        {services.map((service, index) => {
          const IconComponent = service.icon;
          return (
            <div
              key={index}
              className={`bg-white rounded-2xl p-6  border ${service.borderColor} hover:shadow-md transition-shadow ease-in-out`}
            >
              <div className={`w-12 h-12 ${service.bgColor} rounded-xl flex items-center justify-center mb-4`}>
                <IconComponent className={`w-6 h-6 ${service.iconColor}`} />
              </div>
              <h3 className="text-xl font-semibold text-gray-900 mb-3">{service.title}</h3>
              <p className="text-gray-700 leading-relaxed">{service.description}</p>
            </div>
          );
        })}
      </div>
    </section>
  );
}
