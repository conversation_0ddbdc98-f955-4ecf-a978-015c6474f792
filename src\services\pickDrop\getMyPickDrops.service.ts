import { GetMyPickDropsParams, GetMyPickDropsResponse, PickDrop } from "@/types/pickDrop";
import { apiRequest } from "@/utils/axios-utils";

export const getMyPickDrops = async (
  params: GetMyPickDropsParams = {}
): Promise<{
  results: PickDrop[];
  pagination: { page: number; pageSize: number; pageCount: number; total: number };
  summary: {
    total: number;
    pending: number;
    confirmed: number;
    inTransit: number;
    completed: number;
    cancelled: number;
  };
}> => {
  const { page = 1, pageSize = 10, sort = "createdAt:desc" } = params;

  const queryParams = new URLSearchParams();
  queryParams.append("page", page.toString());
  queryParams.append("pageSize", pageSize.toString());
  queryParams.append("sort", sort);

  const response = await apiRequest<GetMyPickDropsResponse>({
    url: `/pick-drops/me?${queryParams.toString()}`,
    method: "GET",
    showToast: false,
  });

  return response.data;
};
