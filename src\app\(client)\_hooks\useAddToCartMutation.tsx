import { useMutation, useQueryClient } from "@tanstack/react-query";
import Cookies from "js-cookie";
import { toast } from "sonner";

import { addToCart } from "@/services/cart/addToCart.service";

export const useAddToCartMutation = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: { productId: number; quantity: number }) => {
      // Check if user is authenticated
      const token = Cookies.get("EEF_AUTH_TOKEN");

      if (!token) {
        // If not authenticated, throw an error to trigger onError
        throw new Error("Authentication required");
      }

      // If authenticated, proceed with adding to cart
      return addToCart(data.productId, data.quantity);
    },
    onSuccess: () => {
      toast.success("Product added to cart successfully.");
      queryClient.invalidateQueries({ queryKey: ["cart-totals"] });
      queryClient.invalidateQueries({ queryKey: ["my-cart"] });
    },
    onError: (error) => {
      if (error instanceof Error && error.message === "Authentication required") {
        toast.error("Please login to add items to your cart");
      } else {
        toast.error("Failed to add product to cart");
      }
    },
  });
};
