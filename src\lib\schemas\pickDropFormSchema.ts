import { z } from "zod";

export const pickDropFormSchema = z.object({
  senderName: z.string().min(2, { message: "Sender name is required" }),
  senderPhoneNumber: z.string().min(10, { message: "Valid phone number is required" }),
  senderAddressLine1: z.string().min(2, { message: "Address line 1 is required" }),
  senderAddressLine2: z.string().optional(),

  receiverName: z.string().min(2, { message: "Receiver name is required" }),
  receiverPhoneNumber: z.string().min(10, { message: "Valid phone number is required" }),
  receiverAddressLine1: z.string().min(2, { message: "Address line 1 is required" }),
  receiverAddressLine2: z.string().optional(),

  itemDescription: z.string().min(5, { message: "Item description is required" }),
  itemWeightKg: z.number().positive({ message: "Weight must be a positive number" }).nullable(),
  itemImage: z.instanceof(File).nullable(),

  pickupLocation: z.string().min(2, { message: "Pickup location is required" }),
  pickupDateTime: z.date({ message: "Pickup date and time is required" }),
  pickupAddress: z.string().min(5, { message: "Pickup address is required" }),

  dropOffLocation: z.string().min(2, { message: "Drop-off location is required" }),
  dropOffDateTime: z.date({ message: "Drop-off date and time is required" }),
  dropOffAddress: z.string().min(5, { message: "Drop-off address is required" }),

  deliveryType: z.string().min(1, { message: "Delivery type is required" }),

  scheduledDateTime: z.date().nullable(),
});

export type PickDropFormValues = z.infer<typeof pickDropFormSchema>;

export const defaultValues: Partial<PickDropFormValues> = {
  senderName: "",
  senderPhoneNumber: "",
  senderAddressLine1: "",
  senderAddressLine2: "",

  receiverName: "",
  receiverPhoneNumber: "",
  receiverAddressLine1: "",
  receiverAddressLine2: "",

  itemDescription: "",
  itemWeightKg: null,

  pickupLocation: "",
  pickupDateTime: new Date(),
  pickupAddress: "",

  dropOffLocation: "",
  dropOffDateTime: new Date(),
  dropOffAddress: "",

  deliveryType: "",
  scheduledDateTime: null,
};
