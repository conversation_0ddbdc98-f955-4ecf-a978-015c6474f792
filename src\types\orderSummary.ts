import { Product } from "@/types/product";

export interface TaxDetail {
  id: number;
  name: string;
  type: string;
  rate: number;
  taxAmount: number;
  description: string | null;
}

export interface DeliveryOption {
  id: number;
  documentId: string;
  amount: number;
  description: string;
  type: string;
  createdAt: string;
  updatedAt: string;
  publishedAt: string;
  locale: string | null;
}

export interface OrderSummaryItem {
  id: number;
  product: Pick<Product, "id" | "price" | "images" | "name">;
  quantity: number;
  subtotal: number;
}

export interface OrderSummary {
  subtotal: number;
  deliveryFee: number;
  tax: number;
  taxDetails: TaxDetail[];
  total: number;
  itemCount: number;
  totalItems: number;
  selectedDeliveryType: string;
  deliveryOptions: DeliveryOption[];
  items: OrderSummaryItem[];
}

export interface OrderSummaryResponse {
  data: OrderSummary;
  meta: null;
}
