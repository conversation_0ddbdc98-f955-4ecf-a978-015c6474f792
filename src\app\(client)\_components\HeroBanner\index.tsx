"use client";

import React from "react";
import { Autoplay, Navigation, Pagination } from "swiper/modules";
import { Swiper, SwiperSlide } from "swiper/react";

import Banner from "@/components/Banner";
import { slidesData } from "@/lib/constants";

import "swiper/css";
import "swiper/css/pagination";
import "swiper/css/navigation";

function HeroBanner() {
  return (
    <div>
      <Swiper
        spaceBetween={30}
        centeredSlides={true}
        autoplay={{
          delay: 3500,
          disableOnInteraction: false,
        }}
        pagination={{
          clickable: true,
        }}
        navigation={false}
        modules={[Autoplay, Pagination, Navigation]}
        className="mySwiper"
      >
        {slidesData.map((slide, index) => (
          <SwiperSlide key={index}>
            <Banner {...slide} />
          </SwiperSlide>
        ))}
      </Swiper>
    </div>
  );
}

export default HeroBanner;
