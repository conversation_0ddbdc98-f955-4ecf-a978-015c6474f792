"use client";

import React from "react";
import { useRouter } from "next/navigation";

import { useCreatePickDropMutation } from "@/app/(client)/_hooks/useCreatePickDropMutation";
import { Form } from "@/components/ui/form";
import { usePickDropForm } from "@/hooks/usePickDropForm";
import { PickDropFormValues } from "@/lib/schemas/pickDropFormSchema";
import Confirmation from "./components/Confirmation";
import DeliveryOption from "./components/DeliveryOption";
import DropOffInfo from "./components/DropOffInfo";
import ItemInfo from "./components/ItemInfo";
import PickUpInfo from "./components/PickupInfo";
import ReceiverInfo from "./components/ReceiverInfo";
import SenderInfo from "./components/SenderInfo";

function PickAndDrop() {
  const router = useRouter();
  const createPickDropMutation = useCreatePickDropMutation();

  const handleOrderPlacement = async (data: PickDropFormValues) => {
    // Create FormData object for sending to backend
    const formData = new FormData();

    // Add all text fields to FormData
    formData.append("senderName", data.senderName);
    formData.append("senderPhoneNumber", data.senderPhoneNumber);
    formData.append("senderAddressLine1", data.senderAddressLine1);
    if (data.senderAddressLine2) formData.append("senderAddressLine2", data.senderAddressLine2);

    formData.append("receiverName", data.receiverName);
    formData.append("receiverPhoneNumber", data.receiverPhoneNumber);
    formData.append("receiverAddressLine1", data.receiverAddressLine1);
    if (data.receiverAddressLine2) formData.append("receiverAddressLine2", data.receiverAddressLine2);

    formData.append("itemDescription", data.itemDescription);
    if (data.itemWeightKg !== null) formData.append("itemWeightKg", data.itemWeightKg.toString());

    formData.append("pickupLocation", data.pickupLocation);
    formData.append("pickupDateTime", data.pickupDateTime.toISOString());
    formData.append("pickupAddress", data.pickupAddress);

    formData.append("dropOffLocation", data.dropOffLocation);
    formData.append("dropOffDateTime", data.dropOffDateTime.toISOString());
    formData.append("dropOffAddress", data.dropOffAddress);

    formData.append("deliveryType", data.deliveryType);
    if (data.scheduledDateTime) formData.append("scheduledDateTime", data.scheduledDateTime.toISOString());

    // Add image file if it exists
    if (data.itemImage) {
      formData.append("itemImage", data.itemImage);
    }

    // Submit the form data using the mutation
    await createPickDropMutation.mutateAsync(formData);

    // Redirect to orders page on success
    router.push("/orders");
  };

  const { form, isSubmitting } = usePickDropForm({
    onSubmit: async (data) => {
      await handleOrderPlacement(data);
    },
  });

  // Combined loading state from both the form and the mutation
  const isLoading = isSubmitting || createPickDropMutation.isPending;

  return (
    <div className="max-width-wrapper mb-20">
      <Form {...form}>
        <form
          onSubmit={form.handleSubmit(handleOrderPlacement)}
          className="grid md:grid-cols-12 gap-8 md:[direction:rtl]"
        >
          <div className="md:[direction:ltr] md:col-span-5">
            <ItemInfo control={form.control} />
          </div>
          <div className="md:[direction:ltr] md:col-span-7 space-y-8">
            <SenderInfo control={form.control} />
            <ReceiverInfo control={form.control} />
            <PickUpInfo control={form.control} />
            <DropOffInfo control={form.control} />
            <DeliveryOption control={form.control} currentStep={5} totalSteps={6} />
            <Confirmation
              submitText={"Place Pick & Drop Order"}
              currentStep={6}
              totalSteps={6}
              isSubmitting={isLoading}
            />
          </div>
        </form>
      </Form>
    </div>
  );
}

export default PickAndDrop;
