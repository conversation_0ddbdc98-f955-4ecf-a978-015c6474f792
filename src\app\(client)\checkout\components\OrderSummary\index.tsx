import React from "react";
import Image from "next/image";
import { Check, ShoppingBag, Truck } from "lucide-react";

import { Skeleton } from "@/components/ui/skeleton";
import { OrderSummary as OrderSummaryType } from "@/types/orderSummary";
import { getApiImage } from "@/utils/getApiImage";

interface OrderSummaryProps {
  data?: OrderSummaryType;
  isLoading: boolean;
  error: Error | null;
}

const OrderSummarySkeleton = () => (
  <>
    <div className="flex items-center justify-between pb-4 border-b">
      <Skeleton className="h-5 w-32" />
      <Skeleton className="h-5 w-16" />
    </div>

    <div className="space-y-4 pb-4">
      {[1, 2, 3].map((i) => (
        <div key={i} className="flex items-center space-x-3">
          <Skeleton className="w-14 h-14 rounded-md" />
          <div className="flex-1 space-y-2">
            <Skeleton className="h-4 w-full" />
            <div className="flex justify-between">
              <Skeleton className="h-3 w-20" />
              <Skeleton className="h-3 w-16" />
            </div>
          </div>
        </div>
      ))}
    </div>

    <div className="pb-4 border-b">
      <Skeleton className="h-5 w-32 mb-2" />
      <Skeleton className="h-16 w-full rounded-md" />
    </div>

    <div className="space-y-3 pb-4 border-b">
      {[1, 2, 3].map((i) => (
        <div key={i} className="flex justify-between items-center">
          <Skeleton className="h-4 w-24" />
          <Skeleton className="h-4 w-16" />
        </div>
      ))}
    </div>

    <div className="flex justify-between items-center pt-2">
      <Skeleton className="h-6 w-16" />
      <Skeleton className="h-7 w-24" />
    </div>
  </>
);

const OrderSummary: React.FC<OrderSummaryProps> = ({ data, isLoading, error }) => {
  if (error) {
    return (
      <div className="w-full lg:sticky top-28 border rounded-lg p-5 tracking-tight space-y-5 bg-red-50 text-red-600">
        <p>Failed to load order summary. Please try again.</p>
      </div>
    );
  }

  return (
    <div className="w-full lg:sticky top-28 border rounded-lg p-5 tracking-tight space-y-5">
      {/* header */}
      <div className="space-y-1 mb-5">
        <h3 className="font-bold text-xl">Order Summary</h3>
        <p className="text-sm text-gray-400 tracking-normal">Review your order details</p>
      </div>

      {isLoading ? (
        <OrderSummarySkeleton />
      ) : data ? (
        <>
          {/* Items summary */}
          <div className="flex items-center justify-between pb-4 border-b">
            <div className="flex items-center gap-2">
              <ShoppingBag size={18} className="text-gray-500" />
              <span className="text-sm text-gray-700">
                {data.itemCount} {data.itemCount === 1 ? "item" : "items"} ({data.totalItems} total)
              </span>
            </div>
            <button className="text-sm text-blue-600 hover:text-blue-800 font-medium">View all</button>
          </div>

          {/* Items preview */}
          <div className="max-h-[30vh] overflow-y-auto space-y-4 pb-4">
            {data.items.map((item) => (
              <div key={item.id} className="flex items-center space-x-3">
                <div className="relative w-14 h-14 rounded-md overflow-hidden bg-gray-50 border">
                  {item.product.images && item.product.images.length > 0 && (
                    <Image
                      src={getApiImage(item.product.images[0].url)}
                      alt={item.product.name || "Product"}
                      layout="fill"
                      objectFit="cover"
                      className="bg-gray-50"
                    />
                  )}
                </div>
                <div className="flex-1">
                  <h6 className="font-medium text-sm text-gray-900 line-clamp-1">
                    {item.product.name || `Product #${item.product.id}`}
                  </h6>
                  <div className="flex justify-between items-center mt-1">
                    <p className="text-xs text-gray-500">
                      {item.product.price} AED × {item.quantity}
                    </p>
                    <p className="text-sm font-medium">{item.subtotal} AED</p>
                  </div>
                </div>
              </div>
            ))}
          </div>

          {/* Delivery method */}
          <div className="pb-4 border-b">
            <div className="flex items-center gap-2 mb-2">
              <Truck size={18} className="text-gray-500" />
              <span className="text-sm font-medium">Delivery Method</span>
            </div>
            <div className="bg-gray-50 rounded-md p-3 flex justify-between items-center">
              <div>
                <p className="text-sm font-medium">{data.selectedDeliveryType} Delivery</p>
              </div>
              <p className="text-sm font-medium">{data.deliveryFee} AED</p>
            </div>
          </div>

          {/* Price breakdown */}
          <div className="space-y-3 pb-4 border-b">
            <div className="flex justify-between items-center">
              <p className="text-sm text-gray-600">Subtotal</p>
              <p className="text-sm">{data.subtotal} AED</p>
            </div>
            <div className="flex justify-between items-center">
              <p className="text-sm text-gray-600">Delivery Fee</p>
              <p className="text-sm">{data.deliveryFee} AED</p>
            </div>
            {data.tax > 0 && (
              <div className="flex justify-between items-center">
                <p className="text-sm text-gray-600">Tax</p>
                <p className="text-sm">{data.tax} AED</p>
              </div>
            )}
            {data.taxDetails.map(
              (tax) =>
                tax.taxAmount > 0 && (
                  <div key={tax.id} className="flex justify-between items-center text-xs text-gray-500 pl-4">
                    <p>
                      {tax.name} ({tax.type} {tax.rate}%)
                    </p>
                    <p>{tax.taxAmount} AED</p>
                  </div>
                )
            )}
          </div>

          {/* Total */}
          <div className="flex justify-between items-center pt-2">
            <p className="font-bold">Total</p>
            <p className="text-xl font-bold">{data.total} AED</p>
          </div>

          {/* Secure checkout message */}
          <div className="flex items-center justify-center gap-2 text-xs text-gray-500 pt-4 border-t">
            <Check size={14} className="text-green-600" />
            <span>Secure checkout powered by EEF Express</span>
          </div>
        </>
      ) : (
        <p className="text-center text-gray-500">No order summary available</p>
      )}
    </div>
  );
};

export default OrderSummary;
