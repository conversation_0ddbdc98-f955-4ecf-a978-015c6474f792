import { Eye } from "lucide-react";

const dataUsageItems = [
  {
    text: "Process and fulfill orders",
    color: "green",
  },
  {
    text: "Deliver products to your address",
    color: "green",
  },
  {
    text: "Communicate order updates",
    color: "green",
  },
  {
    text: "Provide customer support",
    color: "blue",
  },
  {
    text: "Improve our website/app performance and services",
    color: "blue",
  },
  {
    text: "Prevent fraud and enforce legal compliance",
    color: "blue",
  },
];

const getColorClasses = (color: string) => {
  return color === "green" ? "bg-green-500" : "bg-blue-500";
};

export default function HowWeUseInfoSection() {
  const midPoint = Math.ceil(dataUsageItems.length / 2);
  const firstColumn = dataUsageItems.slice(0, midPoint);
  const secondColumn = dataUsageItems.slice(midPoint);

  return (
    <section className="mb-16">
      <div className="flex items-center gap-4 mb-8">
        <div className="flex items-center justify-center w-12 h-12 bg-green-100 rounded-xl">
          <Eye className="w-6 h-6 text-green-600" />
        </div>
        <h2 className="section-title text-gray-900">2. How We Use Your Information</h2>
      </div>
      <p className="text-gray-700 mb-6 leading-relaxed">We use your data to:</p>

      <div className="bg-gradient-to-r from-green-50 to-blue-50 rounded-2xl p-8 border border-green-100">
        <div className="grid md:grid-cols-2 gap-6">
          <ul className="space-y-3">
            {firstColumn.map((item, index) => (
              <li key={index} className="flex items-start gap-3">
                <div
                  className={`w-6 h-6 ${getColorClasses(item.color)} rounded-full flex items-center justify-center mt-0.5`}
                >
                  <div className="w-2 h-2 bg-white rounded-full"></div>
                </div>
                <span className="text-gray-700">{item.text}</span>
              </li>
            ))}
          </ul>
          <ul className="space-y-3">
            {secondColumn.map((item, index) => (
              <li key={index} className="flex items-start gap-3">
                <div
                  className={`w-6 h-6 ${getColorClasses(item.color)} rounded-full flex items-center justify-center mt-0.5`}
                >
                  <div className="w-2 h-2 bg-white rounded-full"></div>
                </div>
                <span className="text-gray-700">{item.text}</span>
              </li>
            ))}
          </ul>
        </div>
      </div>
    </section>
  );
}
