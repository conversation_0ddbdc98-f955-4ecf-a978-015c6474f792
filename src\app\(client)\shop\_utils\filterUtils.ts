import { ProductsFilterParams } from "@/services/products/getProducts.service";

/**
 * Initialize filter parameters from URL search params
 */
export const initializeFiltersFromURL = (searchParams: URLSearchParams): ProductsFilterParams => {
  const initialFilters: ProductsFilterParams = {
    page: Number(searchParams.get("page")) || 1,
    pageSize: Number(searchParams.get("pageSize")) || 12,
  };

  // Add category if present
  if (searchParams.has("category")) {
    initialFilters.category = Number(searchParams.get("category"));
  }

  // Add sort if present
  if (searchParams.has("sort")) {
    initialFilters.sort = searchParams.get("sort") as string;
  }

  // Add price filters if present
  if (searchParams.has("minPrice")) {
    initialFilters.minPrice = Number(searchParams.get("minPrice"));
  }

  if (searchParams.has("maxPrice")) {
    initialFilters.maxPrice = Number(searchParams.get("maxPrice"));
  }

  // Add search if present
  if (searchParams.has("search")) {
    initialFilters.search = searchParams.get("search") as string;
  }

  // Add inStock if present
  if (searchParams.has("inStock")) {
    initialFilters.inStock = searchParams.get("inStock") === "true";
  }

  // Add minRating if present
  if (searchParams.has("minRating")) {
    initialFilters.minRating = Number(searchParams.get("minRating"));
  }

  return initialFilters;
};

/**
 * Convert filters object to URL search params
 */
export const filtersToURLParams = (filters: ProductsFilterParams): URLSearchParams => {
  const params = new URLSearchParams();

  // Add all non-undefined filters to URL
  Object.entries(filters).forEach(([key, value]) => {
    if (value !== undefined) {
      params.set(key, String(value));
    }
  });

  return params;
};
