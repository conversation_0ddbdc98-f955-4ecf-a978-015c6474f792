import { useMutation, useQueryClient } from "@tanstack/react-query";
import { toast } from "sonner";

import { removeItemFromCart } from "@/services/cart/removeItemFromCart.service";

export const useRemoveItemFromCartMutation = () => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: (itemId: number) => removeItemFromCart(itemId),
    onSuccess: () => {
      toast.success("Item removed from cart successfully.");
      queryClient.invalidateQueries({ queryKey: ["cart-totals"] });
      queryClient.invalidateQueries({ queryKey: ["my-cart"] });
    },
  });
};
