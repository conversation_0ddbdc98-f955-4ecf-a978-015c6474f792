"use client";

import React, { useState } from "react";
import { useParams } from "next/navigation";
import { ShoppingCart } from "lucide-react";
import { toast } from "sonner";

import CustomProductViewer from "@/components/CustomProductViewer";
import LoadingSpinner from "@/components/LoadingSpinner";
import Rating from "@/components/Rating";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { getApiImage } from "@/utils/getApiImage";
import { useAddToCartMutation } from "../../_hooks/useAddToCartMutation";
import ProductDetailSkeleton from "./_components/ProductDetailSkeleton";
import { useGetProductDetailQuery } from "./_hooks/useGetProductDetailQuery";

function ProductDetail() {
  const params = useParams();
  const productId = Number(params?.productId);
  const { data: product, isLoading, isError } = useGetProductDetailQuery(productId!);
  const addToCartMutation = useAddToCartMutation();

  // Add state for product quantity
  const [quantity, setQuantity] = useState(1);

  // Functions to handle quantity changes
  const incrementQuantity = () => {
    setQuantity((prev) => prev + 1);
  };

  const decrementQuantity = () => {
    if (quantity > 1) {
      setQuantity((prev) => prev - 1);
    }
  };

  // Handle add to cart
  const handleAddToCart = () => {
    if (product) {
      // when item is out of stock
      if (product.stock === 0) {
        toast.info("Product out of stock.");
        return;
      }

      addToCartMutation.mutate({
        productId: product.id,
        quantity: quantity,
      });
    }
  };

  // Process image URLs for the product viewer
  const imageUrls = product?.images?.map((img) => getApiImage(img.url)) || [];

  // Loading state
  if (isLoading) {
    return <ProductDetailSkeleton />;
  }

  // Error state
  if (isError) {
    return (
      <div className="max-width-wrapper py-8 mb-20">
        <div className="text-center py-12">
          <h2 className="text-xl font-bold text-red-500 mb-2">Failed to load product</h2>
          <p className="text-gray-600">There was an error loading the product details. Please try again later.</p>
          <Button variant="outline" className="mt-4" onClick={() => window.location.reload()}>
            Try Again
          </Button>
        </div>
      </div>
    );
  }

  // If we have data, render the product
  return (
    <div className="max-width-wrapper py-8 mb-20">
      <div className="grid md:grid-cols-2 gap-8">
        {/* Product images */}
        <div className="">
          {imageUrls.length > 0 && <CustomProductViewer images={imageUrls} productName={product?.name || "Product"} />}
        </div>

        {/* Product details */}
        <div className="flex flex-col">
          <h1 className="text-2xl font-bold mb-1 tracking-tight">{product?.name}</h1>

          {/* Rating */}
          <div className="flex items-center mb-4">
            <Rating rating={product?.ratings || 0} />
            <span className="ml-2 text-gray-600">{product?.ratings || 0}+ Reviews</span>
          </div>

          {/* Products in stock */}
          <div className="mb-2">
            {(product?.stock ?? 0) > 0 ? (
              <Badge variant={"outline"} className="bg-white">
                {product?.stock ?? 0} in-stock
              </Badge>
            ) : (
              <Badge variant={"destructive"}>out of stock</Badge>
            )}
          </div>

          {/* Description */}
          <p className="text-gray-700 mb-6">{product?.description}</p>

          {/* Price and Quantity */}
          <div className="">
            <div className="flex items-center space-x-4 mt-2 text-sm border p-2 rounded-full w-fit">
              <Button
                variant={"ghost"}
                size={"icon"}
                className="w-7 h-7 p-0 rounded-full hover:bg-black hover:text-white"
                onClick={decrementQuantity}
                disabled={quantity <= 1}
              >
                -
              </Button>
              <span className="w-5 text-center">{quantity}</span>
              <Button
                variant={"ghost"}
                size={"icon"}
                className="w-7 h-7 p-0 rounded-full hover:bg-black hover:text-white"
                onClick={incrementQuantity}
              >
                +
              </Button>
            </div>
            <div className="mt-6 flex flex-col sm:flex-row items-start justify-between">
              <div className="flex items-baseline mb-6 flex-col tracking-tight">
                <span className="text-2xl font-bold">{((product?.price || 0) * quantity).toFixed(2)} AED</span>
                {product?.price && (
                  <span className="ml-2 text-gray-500 line-through">{(product.price * quantity).toFixed(2)} AED</span>
                )}
              </div>

              {/* Add to cart button */}
              <Button onClick={handleAddToCart} size={"lg"} className="w-full sm:w-fit sm:min-w-42">
                {addToCartMutation.isPending ? (
                  <LoadingSpinner size="sm" />
                ) : (
                  <>
                    <ShoppingCart className="mr-2" />
                    Add to Cart
                  </>
                )}
              </Button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

export default ProductDetail;
