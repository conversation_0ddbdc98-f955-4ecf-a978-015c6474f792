import { useMutation, useQueryClient } from "@tanstack/react-query";
import { toast } from "sonner";

import { updateItemQuantityInCart } from "@/services/cart/updateItemQuantityInCart.service";

export const useUpdateItemQuantityInCartMutation = () => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: (data: { itemId: number; quantity: number }) => updateItemQuantityInCart(data.itemId, data.quantity),
    onSuccess: () => {
      toast.success("Item quantity updated successfully.");
      queryClient.invalidateQueries({ queryKey: ["cart-totals"] });
      queryClient.invalidateQueries({ queryKey: ["my-cart"] });
    },
  });
};
