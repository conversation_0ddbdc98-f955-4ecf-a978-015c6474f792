export const smartTruncate = (text: string, maxLength: number = 45) => {
  if (text.length <= maxLength) return text;

  const truncated = text.substring(0, maxLength);
  const lastSpaceIndex = truncated.lastIndexOf(" ");

  // If there's a space within the last 10 characters, cut at the word boundary
  if (lastSpaceIndex > maxLength - 10) {
    return truncated.substring(0, lastSpaceIndex) + "...";
  }

  return truncated + "...";
};
