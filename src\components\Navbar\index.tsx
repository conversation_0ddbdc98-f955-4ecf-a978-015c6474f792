"use client";

import React from "react";
import Image from "next/image";
import Link from "next/link";
import { usePathname, useRouter } from "next/navigation";
import { ArrowLeft } from "lucide-react";

import { useLoggedInUser } from "@/app/(auth)/_hooks/useLoggedInUser";
import { Sheet, SheetClose, SheetContent, SheetHeader, SheetTitle, SheetTrigger } from "@/components/ui/sheet";
import { navLinks } from "@/lib/constants";
import { removeToken } from "@/utils/remove-token";
import CartSheet from "../CartSheet";
import GlobalSearch from "../GlobalSearch";
import { Icons } from "../Icons/icons";
import NavUser from "../NavUser";
import { Button } from "../ui/button";

export default function Navbar() {
  const pathname = usePathname();
  const router = useRouter();
  const { data: user, isLoading: userLoading } = useLoggedInUser();
  const isHomePage = pathname === "/";

  const handleLogout = () => {
    removeToken();
    window.location.reload();
  };

  const navigateBack = () => {
    const currentPath = pathname?.split("?")[0]; // get base route without query

    if (currentPath === "/shop") {
      router.push("/");
    } else {
      router.back();
    }
  };

  return (
    <>
      <nav className="w-full shadow-sm sticky top-0 z-40 bg-white ios-status-bar-padding">
        <div className="max-width-wrapper flex items-center md:justify-between">
          <Link href={"/"}>
            <Image src="/assets/svgs/logo.svg" width={100} height={57.5} alt="eefexpress" />
          </Link>

          {/* nav links */}
          <div className="hidden items-center gap-4 text-sm md:flex">
            {navLinks.map((navlink, index) => {
              const isActive = pathname === navlink.link;

              return (
                <Link className={`link ${isActive ? "font-bold" : ""}`} key={index} href={navlink.link}>
                  {navlink.text}
                </Link>
              );
            })}
          </div>
          {/* nav links ends */}

          {/* nav buttons */}
          <div className="items-center gap-4 ml-auto mr-4 md:mr-0 md:ml-0 md:flex">
            <GlobalSearch />

            {/* Cart component */}
            <CartSheet />
            {/* Cart component ends */}

            {/* User authentication dropdown */}
            <div className="hidden md:block">
              <NavUser userLoading={userLoading} user={user!} />
            </div>
          </div>
          {/* nav buttons ends */}

          {/* mobile menu */}
          <Sheet>
            <SheetTrigger className="md:hidden border p-1.5">
              <Icons.menu size={"20"} />
            </SheetTrigger>
            <SheetContent side="left" className="pl-14">
              <SheetHeader>
                <SheetTitle className="font-bold text-left">
                  <Link href={"/"}>
                    <Image src="/assets/svgs/logo.svg" width={100} height={80} alt="eefexpress" />
                  </Link>
                </SheetTitle>

                {/* mobile nav links */}
                <div className="flex flex-col items-start gap-4 pt-16">
                  <p className="flex items-center gap-2 text-sm text-gray-500">
                    <span className="size-1.5 bg-gray-500 rounded-full"></span> Have a look around...
                  </p>
                  {navLinks.map((navlink, index) => (
                    <SheetClose asChild key={index}>
                      <Link
                        className="text-2xl w-full text-left font-bold tracking-tighter hover:text-primary"
                        href={navlink.link}
                      >
                        {navlink.text}
                      </Link>
                    </SheetClose>
                  ))}
                  {/* mobile nav links ends */}

                  {/* Mobile auth links */}

                  {user ? (
                    <SheetClose asChild>
                      <Button
                        onClick={handleLogout}
                        variant="link"
                        size="sm"
                        className="text-red-500 text-2xl  text-left font-bold tracking-tighter hover:text-primary p-0 py-1"
                      >
                        Logout
                      </Button>
                    </SheetClose>
                  ) : (
                    <div className="w-full flex flex-col space-y-4 ">
                      <SheetClose asChild>
                        <Link
                          href={"/login"}
                          className="text-2xl w-full text-left font-bold tracking-tighter hover:text-primary"
                        >
                          Login
                        </Link>
                      </SheetClose>
                      <SheetClose asChild>
                        <Link
                          href={"/signup"}
                          className="text-2xl w-full text-left font-bold tracking-tighter hover:text-primary"
                        >
                          Sign up
                        </Link>
                      </SheetClose>
                    </div>
                  )}
                </div>
              </SheetHeader>
            </SheetContent>
          </Sheet>
          {/* mobile ends */}
        </div>
      </nav>
      <div className={`${isHomePage && "hidden"} max-width-wrapper sticky top-20 z-50`}>
        <Button onClick={navigateBack} variant={"outline"} className="bg-white">
          <ArrowLeft />
        </Button>
      </div>
    </>
  );
}
