import React, { useState } from "react";

import useGetDeliveryTypesQuery from "@/app/(client)/_hooks/useGetDeliveryTypesQuery";
import { DateInput } from "@/components/DateInput";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Skeleton } from "@/components/ui/skeleton";
import { cn } from "@/lib/utils";

// Available delivery time slots (9 AM to 8 PM, 1-hour intervals)
const DELIVERY_TIME_SLOTS = [
  "09:00-10:00",
  "10:00-11:00",
  "11:00-12:00",
  "12:00-13:00",
  "13:00-14:00",
  "14:00-15:00",
  "15:00-16:00",
  "16:00-17:00",
  "17:00-18:00",
  "18:00-19:00",
  "19:00-20:00",
];

interface DeliveryOptionProps {
  currentStep: number;
  totalSteps: number;
  onSelect?: (deliveryTypeId: number, scheduledDateTime?: Date | null) => void;
}

const DeliveryOption = ({ currentStep, totalSteps, onSelect }: DeliveryOptionProps) => {
  const [selectedOption, setSelectedOption] = useState<string>("");
  const [selectedDate, setSelectedDate] = useState<Date>(new Date());
  const [selectedTime, setSelectedTime] = useState<string>("");

  // Fetch delivery types from API
  const { data: deliveryTypes, isLoading, error } = useGetDeliveryTypesQuery();

  const createScheduledDateTime = (date: Date, timeSlot: string): Date => {
    const newDate = new Date(date);
    const [hours, minutes] = timeSlot.split("-")[0].split(":").map(Number);

    newDate.setHours(hours, minutes, 0, 0);
    return newDate;
  };

  const handleOptionChange = (value: string) => {
    setSelectedOption(value);

    // Find the selected delivery type
    const selectedType = deliveryTypes?.find((type) => type.id.toString() === value);

    // Call onSelect if provided
    if (onSelect && selectedType) {
      if (selectedType.type === "Scheduled" && selectedTime) {
        const scheduledDateTime = createScheduledDateTime(selectedDate, selectedTime);
        onSelect(selectedType.id, scheduledDateTime);
      } else {
        onSelect(selectedType.id, null);
      }
    }
  };

  const handleDateChange = (date: Date | undefined) => {
    if (date) {
      setSelectedDate(date);

      // Update selection if needed
      if (selectedOption && onSelect && selectedTime) {
        const selectedType = deliveryTypes?.find((type) => type.id.toString() === selectedOption);
        if (selectedType && selectedType.type === "Scheduled") {
          const scheduledDateTime = createScheduledDateTime(date, selectedTime);
          onSelect(selectedType.id, scheduledDateTime);
        }
      }
    }
  };

  const handleTimeChange = (value: string) => {
    // Only update if the value is different to prevent unnecessary re-renders
    if (value !== selectedTime) {
      setSelectedTime(value);

      // Update selection if needed
      if (selectedOption && onSelect) {
        const selectedType = deliveryTypes?.find((type) => type.id.toString() === selectedOption);
        if (selectedType && selectedType.type === "Scheduled") {
          const scheduledDateTime = createScheduledDateTime(selectedDate, value);
          onSelect(selectedType.id, scheduledDateTime);
        }
      }
    }
  };

  return (
    <div className="w-full border rounded-lg p-5 tracking-tight space-y-5">
      {/* header */}
      <div className="mb-8 flex justify-between items-end">
        <div className="space-y-1">
          <h3 className="font-bold text-xl">Delivery Option</h3>
          <p className="text-sm text-gray-400 tracking-normal">Please select the type of delivery you want</p>
        </div>

        <p className="text-gray-400 text-sm">
          Step {currentStep} of {totalSteps}
        </p>
      </div>
      {/* header ends */}

      {/* body */}
      {isLoading ? (
        // Loading state
        <div className="space-y-4">
          {[1, 2, 3, 4].map((i) => (
            <div key={i} className="rounded-lg border p-4">
              <div className="flex items-start space-x-3">
                <Skeleton className="h-4 w-4 rounded-full mt-1" />
                <div className="flex-1">
                  <div className="flex justify-between items-center">
                    <Skeleton className="h-5 w-40" />
                    <Skeleton className="h-4 w-16" />
                  </div>
                  <Skeleton className="h-4 w-full mt-1" />
                </div>
              </div>
            </div>
          ))}
        </div>
      ) : error ? (
        // Error state
        <div className="p-4 border border-red-200 bg-red-50 rounded-lg text-red-600">
          Failed to load delivery options. Please try again later.
        </div>
      ) : (
        // When data is available
        <div className="space-y-4">
          {deliveryTypes?.map((deliveryType) => {
            const isSelected = selectedOption === deliveryType.id.toString();

            return (
              <div
                key={deliveryType.id}
                className={cn(
                  "rounded-lg border p-4 cursor-pointer hover:border-gray-400 transition-colors",
                  isSelected && "bg-gray-50 border-gray-500"
                )}
                onClick={() => handleOptionChange(deliveryType.id.toString())}
              >
                <div className="flex items-start space-x-3">
                  {/* CUSTOM RADIO BUTTON START */}
                  {/* This is a container for the radio button */}
                  <div className="relative flex h-4 w-4 items-center justify-center mt-1">
                    {/* This is the outer circle of the radio button */}
                    <div
                      className={cn(
                        "h-4 w-4 rounded-full border border-gray-300",
                        // When selected, make the border thicker and black
                        isSelected && "border-2 border-black"
                      )}
                    >
                      {/* This is the inner dot that appears when selected */}
                      {isSelected && (
                        <div className="absolute inset-0 flex items-center justify-center">
                          <div className="h-2 w-2 rounded-full bg-black"></div>
                        </div>
                      )}
                    </div>
                  </div>
                  {/* CUSTOM RADIO BUTTON END */}

                  <div className="flex-1">
                    <div className="flex justify-between items-center">
                      <Label className="font-medium text-base cursor-pointer">{deliveryType.type} Delivery</Label>
                      <span className="text-sm font-medium text-gray-500">{deliveryType.amount} AED</span>
                    </div>
                    <p className="text-sm text-gray-500 mt-1">
                      {deliveryType.description}. {deliveryType.estimatedTime}.
                    </p>

                    {/* Date and Time fields for Scheduled delivery */}
                    {deliveryType.type === "Scheduled" && (
                      <div
                        className={cn(
                          "mt-4 grid grid-cols-1 md:grid-cols-2 gap-4 overflow-hidden transition-all duration-300 ease-in-out",
                          isSelected ? "max-h-40 opacity-100" : "max-h-0 opacity-0"
                        )}
                        style={{ pointerEvents: isSelected ? "auto" : "none" }}
                        onClick={(e) => e.stopPropagation()}
                      >
                        <div>
                          <Label htmlFor="deliveryDate" className="text-sm mb-1.5 block">
                            Date
                          </Label>
                          <DateInput id="deliveryDate" defaultValue={selectedDate} onDateChange={handleDateChange} />
                        </div>
                        <div>
                          <Label htmlFor="deliveryTime" className="text-sm mb-1.5 block">
                            Time Slot
                          </Label>
                          <Select value={selectedTime} onValueChange={handleTimeChange}>
                            <SelectTrigger className="bg-white w-full">
                              <SelectValue placeholder="Select delivery time" />
                            </SelectTrigger>
                            <SelectContent>
                              {DELIVERY_TIME_SLOTS.map((slot) => (
                                <SelectItem key={slot} value={slot}>
                                  {slot}
                                </SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                        </div>
                      </div>
                    )}
                  </div>
                </div>
              </div>
            );
          })}
        </div>
      )}
    </div>
  );
};

export default DeliveryOption;
