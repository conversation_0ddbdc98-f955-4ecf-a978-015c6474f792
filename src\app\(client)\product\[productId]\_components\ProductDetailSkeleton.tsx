import React from "react";

import { Skeleton } from "@/components/ui/skeleton";

function ProductDetailSkeleton() {
  return (
    <div className="max-width-wrapper py-8 mb-20">
      <div className="grid md:grid-cols-2 gap-8">
        {/* Product images skeleton */}
        <div>
          <Skeleton className="h-[22rem] w-full rounded-lg" />
          <div className="flex gap-2 mt-4">
            {[1, 2, 3, 4].map((i) => (
              <Skeleton key={i} className="h-16 w-16 rounded-md" />
            ))}
          </div>
        </div>

        {/* Product details skeleton */}
        <div className="flex flex-col space-y-4">
          <Skeleton className="h-8 w-3/4" />
          <div className="flex gap-5 items-center">
            <Skeleton className="h-5 w-1/5" />
            <Skeleton className="h-3.5 w-1/8" />
          </div>
          <div className="space-y-2">
            <Skeleton className="h-4 w-full" />
            <Skeleton className="h-4 w-full" />
            <Skeleton className="h-4 w-full" />
            <Skeleton className="h-4 w-full" />
          </div>
          <Skeleton className="h-10 w-1/4" />
          <div className="mt-6 gap-3 flex flex-col sm:flex-row items-start justify-between">
            <Skeleton className="h-12 w-1/3" />
            <Skeleton className="h-12 w-full sm:w-2/7" />
          </div>
        </div>
      </div>
    </div>
  );
}

export default ProductDetailSkeleton;
