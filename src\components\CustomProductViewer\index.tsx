"use client";

import React, { useEffect, useState } from "react";
import Image from "next/image";

import { Card, CardContent } from "@/components/ui/card";
import {
  Carousel,
  CarouselContent,
  CarouselItem,
  CarouselNext,
  CarouselPrevious,
  type CarouselApi,
} from "@/components/ui/carousel";
import { cn } from "@/lib/utils";

interface ProductImageViewerProps {
  images: string[];
  productName: string;
}

const CustomProductViewer = ({ images, productName }: ProductImageViewerProps) => {
  const [api, setApi] = useState<CarouselApi>();
  const [current, setCurrent] = useState(0);
  // const [count, setCount] = useState(0);

  useEffect(() => {
    if (!api) {
      return;
    }

    // setCount(api.scrollSnapList().length);
    setCurrent(api.selectedScrollSnap());

    api.on("select", () => {
      setCurrent(api.selectedScrollSnap());
    });
  }, [api]);

  const handleThumbClick = React.useCallback(
    (index: number) => {
      api?.scrollTo(index);
    },
    [api]
  );

  // If no images are provided, show a placeholder
  if (!images.length) {
    return (
      <div className="product-viewer w-full">
        <div className="h-[22rem] bg-gray-100 rounded-lg flex items-center justify-center">
          <p className="text-gray-400">No images available</p>
        </div>
      </div>
    );
  }

  return (
    <div className="product-viewer w-full">
      {/* Main image carousel */}
      <Carousel setApi={setApi} className="w-full">
        <CarouselContent>
          {images.map((img, index) => (
            <CarouselItem key={index}>
              <div className="h-[22rem] bg-gray-50 rounded-lg flex items-center justify-center">
                <Image
                  src={img}
                  alt={`${productName} - Image ${index + 1}`}
                  width={250}
                  height={250}
                  className="object-contain aspect-square"
                  priority={index === 0}
                />
              </div>
              {/* </CardContent>
              </Card> */}
            </CarouselItem>
          ))}
        </CarouselContent>
        {images.length > 1 && (
          <>
            <CarouselPrevious className="left-2" />
            <CarouselNext className="right-2" />
          </>
        )}
      </Carousel>

      {/* Thumbnails carousel - only show if we have more than 1 image */}
      {images.length > 1 && (
        <Carousel className="mt-4 w-full">
          <CarouselContent className="flex gap-2">
            {images.map((img, index) => (
              <CarouselItem
                key={index}
                className={cn(
                  "basis-1/5 md:basis-1/6 cursor-pointer",
                  current === index ? "opacity-100" : "opacity-80"
                )}
                onClick={() => handleThumbClick(index)}
              >
                <Card
                  className={cn(
                    "border overflow-hidden h-full aspect-square p-0",
                    current === index ? "border-2 border-blue-500" : "border border-gray-200"
                  )}
                >
                  <CardContent className="p-0 h-full ">
                    <div className="aspect-square w-full h-full relative">
                      <Image
                        src={img}
                        alt={`${productName} thumbnail ${index + 1}`}
                        fill
                        sizes="(max-width: 768px) 20vw, 16vw"
                        className="object-cover aspect-square"
                      />
                    </div>
                  </CardContent>
                </Card>
              </CarouselItem>
            ))}
          </CarouselContent>
          {images.length > 5 && (
            <>
              <CarouselPrevious className="left-2 h-7 w-7" />
              <CarouselNext className="right-2 h-7 w-7" />
            </>
          )}
        </Carousel>
      )}
    </div>
  );
};

export default CustomProductViewer;
