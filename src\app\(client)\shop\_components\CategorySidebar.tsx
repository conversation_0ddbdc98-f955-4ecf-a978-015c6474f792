"use client";

import React, { useEffect, useState } from "react";
import { <PERSON>Todo, SlidersHorizontal, Star } from "lucide-react";

import CategoryList from "@/components/CategoryList";
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from "@/components/ui/accordion";
import { Button } from "@/components/ui/button";
import { Checkbox } from "@/components/ui/checkbox";
import { Label } from "@/components/ui/label";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { Sheet, SheetClose, SheetContent, SheetHeader, SheetTitle, SheetTrigger } from "@/components/ui/sheet";
import { Slider } from "@/components/ui/slider";
import { ProductsFilterParams } from "@/services/products/getProducts.service";

interface CategorySidebarProps {
  onCategoryClick: (categoryId: number) => void;
  selectedCategoryId?: number;
  filters: ProductsFilterParams;
  onFilterChange: (newFilters: Partial<ProductsFilterParams>) => void;
}

function CategorySidebar({ onCategoryClick, selectedCategoryId, filters = {}, onFilterChange }: CategorySidebarProps) {
  const [priceRange, setPriceRange] = useState<[number, number]>([0, 1000]);

  // Initialize state from props when filters change
  useEffect(() => {
    setPriceRange([
      filters.minPrice !== undefined ? filters.minPrice : 0,
      filters.maxPrice !== undefined ? filters.maxPrice : 1000,
    ]);
  }, [filters.minPrice, filters.maxPrice]);

  // Handle price range change
  const handlePriceChange = (value: number[]) => {
    setPriceRange([value[0], value[1]]);
  };

  // Apply price filter
  const applyPriceFilter = () => {
    if (onFilterChange) {
      onFilterChange({
        minPrice: priceRange[0],
        maxPrice: priceRange[1],
        page: 1, // Reset to page 1 when changing filters
      });
    }
  };

  // Handle sort change
  const handleSortChange = (value: string) => {
    if (onFilterChange) {
      onFilterChange({
        sort: value,
        page: 1,
      });
    }
  };

  // Handle stock filter change
  const handleStockChange = (checked: boolean) => {
    if (onFilterChange) {
      onFilterChange({
        inStock: checked,
        page: 1,
      });
    }
  };

  // Handle rating filter change
  const handleRatingChange = (value: number) => {
    if (onFilterChange) {
      onFilterChange({
        minRating: value,
        page: 1,
      });
    }
  };

  // Reset all filters
  const resetFilters = () => {
    setPriceRange([0, 1000]);
    if (onFilterChange) {
      onFilterChange({
        minPrice: undefined,
        maxPrice: undefined,
        search: undefined,
        sort: undefined,
        inStock: undefined,
        minRating: undefined,
        category: undefined,
        page: 1,
      });
    }
  };

  const FilterContent = () => (
    <div className="space-y-6">
      {/* Price range section */}
      <div className="space-y-4">
        <h2 className="text-sm font-bold tracking-wide text-[#013356] uppercase">Price Range</h2>
        <div className="px-2">
          <Slider
            defaultValue={[0, 1000]}
            max={1000}
            step={10}
            value={[priceRange[0], priceRange[1]]}
            onValueChange={handlePriceChange}
            className="my-6"
          />
          <div className="flex items-center justify-between">
            <span className="text-sm text-gray-500">{priceRange[0]} AED</span>
            <span className="text-sm text-gray-500">{priceRange[1]} AED</span>
          </div>
          <Button onClick={applyPriceFilter} size="sm" className="w-full mt-2">
            Apply
          </Button>
        </div>
      </div>

      <div className="h-px bg-gray-200" />

      {/* Sort by section */}
      <div className="space-y-4">
        <h2 className="text-sm font-bold tracking-wide text-[#013356] uppercase">Sort By</h2>
        <RadioGroup value={filters.sort || ""} onValueChange={handleSortChange} className="space-y-2">
          <div className="flex items-center space-x-2">
            <RadioGroupItem value="price:asc" id="price_asc" />
            <Label htmlFor="price_asc" className="text-sm">
              Price: Low to High
            </Label>
          </div>
          <div className="flex items-center space-x-2">
            <RadioGroupItem value="price:desc" id="price_desc" />
            <Label htmlFor="price_desc" className="text-sm">
              Price: High to Low
            </Label>
          </div>
          <div className="flex items-center space-x-2">
            <RadioGroupItem value="createdAt:desc" id="newest" />
            <Label htmlFor="newest" className="text-sm">
              Newest First
            </Label>
          </div>
        </RadioGroup>
      </div>

      <div className="h-px bg-gray-200" />

      {/* Availability section */}
      <div className="space-y-4">
        <h2 className="text-sm font-bold tracking-wide text-[#013356] uppercase">Availability</h2>
        <div className="flex items-center space-x-2">
          <Checkbox id="in-stock" checked={filters.inStock || false} onCheckedChange={handleStockChange} />
          <Label htmlFor="in-stock" className="text-sm">
            In Stock Only
          </Label>
        </div>
      </div>

      <div className="h-px bg-gray-200" />

      {/* Rating section */}
      <div className="space-y-4">
        <h2 className="text-sm font-bold tracking-wide text-[#013356] uppercase">Rating</h2>
        <div className="space-y-2">
          {[4, 3, 2, 1].map((rating) => (
            <div
              key={rating}
              className="flex items-center space-x-2 cursor-pointer"
              onClick={() => handleRatingChange(rating)}
            >
              <div className="flex">
                {Array(5)
                  .fill(0)
                  .map((_, i) => (
                    <Star
                      key={i}
                      size={16}
                      className={i < rating ? "fill-yellow-400 text-yellow-400" : "text-gray-300"}
                    />
                  ))}
              </div>
              <span className="text-sm text-gray-600">& Up</span>
            </div>
          ))}
        </div>
      </div>

      <div className="h-px bg-gray-200" />

      {/* Reset filters */}
      <Button variant="outline" size="sm" onClick={resetFilters} className="w-full">
        Reset All Filters
      </Button>
    </div>
  );

  return (
    <>
      {/* Desktop filters */}
      <div className="hidden md:block md:col-span-4 lg:col-span-3 md:p-4 md:border-r md:border-gray-100">
        <Accordion className="mb-6" type="single" collapsible>
          <AccordionItem value="categories">
            <AccordionTrigger className="text-sm font-bold tracking-wide text-[#013356] uppercase">
              Categories
            </AccordionTrigger>
            <AccordionContent>
              <CategoryList onCategoryClick={onCategoryClick} selectedCategoryId={selectedCategoryId} />
            </AccordionContent>
          </AccordionItem>
        </Accordion>
        <FilterContent />
      </div>

      {/* Mobile filters */}
      <div className="md:hidden flex space-x-2">
        <Sheet>
          <SheetTrigger className="p-2 px-4 rounded-lg border hover:bg-gray-100 w-fit">
            <span className="flex items-center gap-2">
              <ListTodo /> Categories
            </span>
          </SheetTrigger>
          <SheetContent side="right" className="p-6 overflow-y-auto">
            <SheetHeader className="mb-4 pl-0">
              <SheetTitle className="font-bold text-left tracking-tighter text-xl">Categories</SheetTitle>
            </SheetHeader>
            <div className="pl-0 md:pl-20">
              <SheetClose asChild>
                <CategoryList onCategoryClick={onCategoryClick} selectedCategoryId={selectedCategoryId} />
              </SheetClose>
            </div>
          </SheetContent>
        </Sheet>

        <Sheet>
          <SheetTrigger className="p-2 px-4 rounded-lg border hover:bg-gray-100 w-fit">
            <span className="flex items-center gap-2">
              <SlidersHorizontal size={18} /> Filters
            </span>
          </SheetTrigger>
          <SheetContent side="right" className="p-6 overflow-y-auto">
            <SheetHeader className="mb-4 pl-1">
              <SheetTitle className="font-bold text-left tracking-tighter text-xl">Filters</SheetTitle>
            </SheetHeader>
            <div className="px-2">
              <FilterContent />
            </div>
          </SheetContent>
        </Sheet>
      </div>
    </>
  );
}

export default CategorySidebar;
