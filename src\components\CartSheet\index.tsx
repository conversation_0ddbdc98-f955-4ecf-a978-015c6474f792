"use client";

import React, { useEffect, useState } from "react";
import Link from "next/link";
import Cookies from "js-cookie";

import { But<PERSON> } from "@/components/ui/button";
import {
  Sheet,
  She<PERSON><PERSON>lose,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
} from "@/components/ui/sheet";
import { Skeleton } from "@/components/ui/skeleton";
import { useCartTotalQuery } from "@/hooks/cart/useCartTotalQuery";
import { useGetMyCartQuery } from "@/hooks/cart/useGetMyCartQuery";
import type { CartItem as CartItemType } from "@/types/cart.js";
import CartButton from "../CartButton.tsx";
import CartItem from "../CartItem";

export default function CartSheet() {
  const [isAuthenticated, setIsAuthenticated] = useState<boolean>(false);

  useEffect(() => {
    const token = Cookies.get("EEF_AUTH_TOKEN");
    setIsAuthenticated(!!token);
  }, []);

  // Only fetch cart data if user is authenticated
  const { data: cart, isLoading, isError } = useGetMyCartQuery();
  const { data: cartTotals } = useCartTotalQuery();

  const renderLoadingSkeleton = () => (
    <>
      {Array(3)
        .fill(0)
        .map((_, index) => (
          <div key={index} className="flex items-center space-x-4 border-b p-5">
            <Skeleton className="h-16 w-16 rounded-md" />
            <div className="space-y-2 flex-1">
              <Skeleton className="h-4 w-3/4" />
              <Skeleton className="h-4 w-1/4" />
            </div>
          </div>
        ))}
    </>
  );

  const renderError = () => (
    <div className="p-5 text-center">
      <p className="text-red-500">Failed to load cart items</p>
      <Button variant="outline" className="mt-2" onClick={() => window.location.reload()}>
        Try Again
      </Button>
    </div>
  );

  const renderEmptyCart = () => (
    <div className="p-5 text-center">
      <p className="text-gray-500">Your cart is empty</p>
      <SheetClose asChild>
        <Link href="/shop">
          <Button variant="outline" className="mt-2">
            Continue Shopping
          </Button>
        </Link>
      </SheetClose>
    </div>
  );

  const renderUnauthenticated = () => (
    <div className="p-5 text-center">
      <p className="text-gray-500 mb-2">Please login to view your cart</p>
      <SheetClose asChild>
        <Link href="/login">
          <Button className="mt-2">Login</Button>
        </Link>
      </SheetClose>
    </div>
  );

  const renderCartItems = () => <>{cart?.item?.map((item: CartItemType) => <CartItem key={item.id} item={item} />)}</>;

  const isCartEmpty = isAuthenticated && !isLoading && !isError && cart?.item?.length === 0;
  const hasCartItems = isAuthenticated && !isLoading && !isError && Boolean(cart?.item && cart.item.length > 0);

  return (
    <Sheet>
      <SheetTrigger className="p-2 px-4 rounded-lg hover:bg-gray-100">
        <CartButton />
      </SheetTrigger>

      <SheetContent side="right" className="pl-5 w-full sm:max-w-sm">
        <SheetHeader>
          <SheetTitle className="font-bold text-left tracking-tighter text-xl">My Cart</SheetTitle>
        </SheetHeader>

        <div className="tracking-tight max-h-[75vh] overflow-y-auto">
          {!isAuthenticated && renderUnauthenticated()}
          {isAuthenticated && isLoading && renderLoadingSkeleton()}
          {isAuthenticated && isError && renderError()}
          {isCartEmpty && renderEmptyCart()}
          {hasCartItems && renderCartItems()}
        </div>

        <SheetFooter>
          <div className="flex justify-between bg-gray-100 items-center p-3 border-t">
            <p className="text-sm text-gray-500">Subtotal</p>
            <p className="text-sm text-gray-900">
              {!isAuthenticated ? (
                "0 AED"
              ) : isLoading ? (
                <Skeleton className="h-4 w-16" />
              ) : (
                `${cartTotals?.subtotal || 0} AED`
              )}
            </p>
          </div>
          <SheetClose asChild>
            {!isAuthenticated ? (
              <Link href="/login">
                <Button size="lg" className="w-full">
                  Login to Checkout
                </Button>
              </Link>
            ) : (
              <Link href="/checkout">
                <Button size="lg" className="w-full" disabled={isLoading || isError || isCartEmpty}>
                  Proceed to Checkout
                </Button>
              </Link>
            )}
          </SheetClose>
        </SheetFooter>
      </SheetContent>
    </Sheet>
  );
}
