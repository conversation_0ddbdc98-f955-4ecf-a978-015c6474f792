"use client";

import { useRouter } from "next/navigation";
import { useMutation } from "@tanstack/react-query";
import { toast } from "sonner";

import { CheckoutFormValues } from "@/lib/schemas/checkoutFormSchema";
import { createOrder } from "@/services/checkout/createOrder.service";
import { OrderData } from "@/types/order";

export const useCreateOrderMutation = () => {
  const router = useRouter();

  return useMutation({
    mutationFn: (data: CheckoutFormValues) => createOrder(data),
    onSuccess: (response: OrderData) => {
      if (response.success) {
        if (response.payment.paymentMethod === "cash_on_delivery") {
          toast.success("Order placed successfully!");
          router.push("/orders");
        } else if (response.payment.paymentMethod === "card") {
          toast.info("Redirecting to payment gateway...");

          // Check if we have a checkout URL and redirect to it
          if (response.checkoutUrl) {
            // Redirect to Stripe checkout
            window.location.href = response.checkoutUrl;
          }
        }
      }
    },
    onError: () => {
      toast.error("Failed to place order. Please try again.");
    },
  });
};
